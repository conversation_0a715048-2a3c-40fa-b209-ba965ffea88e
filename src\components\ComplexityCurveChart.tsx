import React from 'react';
import { TrendingUp, BarChart3 } from 'lucide-react';

interface ComplexityCurveChartProps {
  complexity: string;
  className?: string;
}

const complexityData = {
  'O(1)': {
    color: '#10b981',
    points: Array.from({ length: 50 }, (_, i) => ({ x: i + 1, y: 1 })),
    label: 'Constant',
    description: 'Always takes the same time regardless of input size'
  },
  'O(log n)': {
    color: '#22c55e',
    points: Array.from({ length: 50 }, (_, i) => ({ x: i + 1, y: Math.log2(i + 1) })),
    label: 'Logarithmic',
    description: 'Grows slowly as input size increases'
  },
  'O(n)': {
    color: '#eab308',
    points: Array.from({ length: 50 }, (_, i) => ({ x: i + 1, y: i + 1 })),
    label: 'Linear',
    description: 'Time grows proportionally with input size'
  },
  'O(n log n)': {
    color: '#f97316',
    points: Array.from({ length: 50 }, (_, i) => ({ x: i + 1, y: (i + 1) * Math.log2(i + 1) })),
    label: 'Linearithmic',
    description: 'Common in efficient sorting algorithms'
  },
  'O(n^2)': {
    color: '#ef4444',
    points: Array.from({ length: 50 }, (_, i) => ({ x: i + 1, y: Math.pow(i + 1, 2) })),
    label: 'Quadratic',
    description: 'Time grows quadratically with input size'
  },
  'O(n^3)': {
    color: '#dc2626',
    points: Array.from({ length: 50 }, (_, i) => ({ x: i + 1, y: Math.pow(i + 1, 3) })),
    label: 'Cubic',
    description: 'Time grows cubically with input size'
  },
  'O(2^n)': {
    color: '#9333ea',
    points: Array.from({ length: 20 }, (_, i) => ({ x: i + 1, y: Math.pow(2, i + 1) })),
    label: 'Exponential',
    description: 'Time doubles with each additional input'
  }
};

export const ComplexityCurveChart: React.FC<ComplexityCurveChartProps> = ({ 
  complexity, 
  className = '' 
}) => {
  const data = complexityData[complexity as keyof typeof complexityData];
  
  if (!data) {
    return (
      <div className={`bg-gray-800 rounded-lg p-6 border border-gray-700 ${className}`}>
        <div className="text-center text-gray-400">
          <BarChart3 className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>Complexity curve not available for {complexity}</p>
        </div>
      </div>
    );
  }

  // Normalize data for visualization
  const maxY = Math.max(...data.points.map(p => p.y));
  const normalizedPoints = data.points.map(p => ({
    x: p.x,
    y: Math.min((p.y / maxY) * 100, 100) // Cap at 100% height
  }));

  // Create SVG path
  const pathData = normalizedPoints
    .map((point, index) => {
      const x = (point.x / normalizedPoints.length) * 100;
      const y = 100 - point.y; // Invert Y for SVG coordinate system
      return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
    })
    .join(' ');

  return (
    <div className={`bg-gray-800 rounded-lg p-6 border border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <TrendingUp className="w-5 h-5 mr-2" style={{ color: data.color }} />
          Growth Curve: {data.label}
        </h3>
        <div 
          className="px-3 py-1 rounded-full text-xs font-mono font-bold"
          style={{ 
            backgroundColor: `${data.color}20`, 
            color: data.color,
            border: `1px solid ${data.color}40`
          }}
        >
          {complexity}
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative bg-gray-900/50 rounded-lg p-4 mb-4">
        <svg
          viewBox="0 0 100 100"
          className="w-full h-32 overflow-visible"
          preserveAspectRatio="none"
        >
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#374151" strokeWidth="0.5" opacity="0.3"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" />
          
          {/* Complexity curve */}
          <path
            d={pathData}
            fill="none"
            stroke={data.color}
            strokeWidth="2"
            vectorEffect="non-scaling-stroke"
          />
          
          {/* Fill area under curve */}
          <path
            d={`${pathData} L 100 100 L 0 100 Z`}
            fill={data.color}
            fillOpacity="0.1"
          />
          
          {/* Data points */}
          {normalizedPoints.slice(0, 10).map((point, index) => (
            <circle
              key={index}
              cx={(point.x / normalizedPoints.length) * 100}
              cy={100 - point.y}
              r="1"
              fill={data.color}
              vectorEffect="non-scaling-stroke"
            />
          ))}
        </svg>
        
        {/* Axis labels */}
        <div className="absolute bottom-1 left-1 text-xs text-gray-400">
          Input Size (n)
        </div>
        <div className="absolute top-1 left-1 text-xs text-gray-400 transform -rotate-90 origin-left">
          Time
        </div>
      </div>

      {/* Description */}
      <p className="text-sm text-gray-300 leading-relaxed">
        {data.description}
      </p>

      {/* Performance indicators */}
      <div className="mt-4 grid grid-cols-3 gap-3">
        <div className="bg-gray-900/50 rounded-lg p-3 text-center">
          <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">Small Input</div>
          <div className="text-sm font-medium" style={{ color: data.color }}>
            {complexity === 'O(1)' ? 'Excellent' :
             complexity === 'O(log n)' ? 'Excellent' :
             complexity === 'O(n)' ? 'Good' :
             complexity === 'O(n log n)' ? 'Good' :
             complexity === 'O(n^2)' ? 'Fair' :
             complexity === 'O(n^3)' ? 'Poor' : 'Very Poor'}
          </div>
        </div>
        <div className="bg-gray-900/50 rounded-lg p-3 text-center">
          <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">Medium Input</div>
          <div className="text-sm font-medium" style={{ color: data.color }}>
            {complexity === 'O(1)' ? 'Excellent' :
             complexity === 'O(log n)' ? 'Excellent' :
             complexity === 'O(n)' ? 'Good' :
             complexity === 'O(n log n)' ? 'Fair' :
             complexity === 'O(n^2)' ? 'Poor' :
             complexity === 'O(n^3)' ? 'Very Poor' : 'Unusable'}
          </div>
        </div>
        <div className="bg-gray-900/50 rounded-lg p-3 text-center">
          <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">Large Input</div>
          <div className="text-sm font-medium" style={{ color: data.color }}>
            {complexity === 'O(1)' ? 'Excellent' :
             complexity === 'O(log n)' ? 'Excellent' :
             complexity === 'O(n)' ? 'Good' :
             complexity === 'O(n log n)' ? 'Good' :
             complexity === 'O(n^2)' ? 'Very Poor' :
             complexity === 'O(n^3)' ? 'Unusable' : 'Unusable'}
          </div>
        </div>
      </div>
    </div>
  );
};