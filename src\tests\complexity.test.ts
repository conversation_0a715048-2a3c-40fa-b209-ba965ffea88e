import { describe, it, expect } from 'vitest';
import { enhancedComplexityAnalyzer } from '../utils/enhancedComplexityAnalyzer';

describe('Complexity Analysis', () => {
  describe('Basic Algorithms', () => {
    it('should correctly analyze linear search', () => {
      const code = `
        int linearSearch(int arr[], int n, int target) {
          for (int i = 0; i < n; i++) {
            if (arr[i] == target) return i;
          }
          return -1;
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.complexity).toBe('O(n)');
      expect(result.details.loops).toBe(1);
      expect(result.details.nestingLevel).toBe(1);
    });

    it('should correctly analyze bubble sort', () => {
      const code = `
        void bubbleSort(int arr[], int n) {
          for (int i = 0; i < n - 1; i++) {
            for (int j = 0; j < n - i - 1; j++) {
              if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
              }
            }
          }
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.complexity).toBe('O(n^2)');
      expect(result.details.loops).toBe(2);
      expect(result.details.nestingLevel).toBe(2);
    });

    it('should correctly analyze binary search', () => {
      const code = `
        int binarySearch(int arr[], int l, int r, int x) {
          if (r >= l) {
            int mid = l + (r - l) / 2;
            if (arr[mid] == x) return mid;
            if (arr[mid] > x)
              return binarySearch(arr, l, mid - 1, x);
            return binarySearch(arr, mid + 1, r, x);
          }
          return -1;
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.complexity).toBe('O(log n)');
      expect(result.details.recursiveCalls).toBeGreaterThan(0);
    });

    it('should correctly analyze fibonacci', () => {
      const code = `
        int fibonacci(int n) {
          if (n <= 1) return n;
          return fibonacci(n - 1) + fibonacci(n - 2);
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.complexity).toBe('O(2^n)');
      expect(result.details.recursiveCalls).toBe(2);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty code', () => {
      const result = enhancedComplexityAnalyzer.analyzeCode('', 'cpp');
      expect(result.complexity).toBe('O(1)');
    });

    it('should handle syntax errors gracefully', () => {
      const code = 'invalid syntax {{{';
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.complexity).toBeDefined();
    });
  });

  describe('Language Support', () => {
    it('should analyze Python code', () => {
      const code = `
        def linear_search(arr, target):
          for i in range(len(arr)):
            if arr[i] == target:
              return i
          return -1
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'python');
      expect(result.complexity).toBe('O(n)');
    });

    it('should analyze JavaScript code', () => {
      const code = `
        function linearSearch(arr, target) {
          for (let i = 0; i < arr.length; i++) {
            if (arr[i] === target) return i;
          }
          return -1;
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'javascript');
      expect(result.complexity).toBe('O(n)');
    });
  });

  describe('Space Complexity Analysis', () => {
    it('should analyze constant space algorithms', () => {
      const code = `
        int linearSearch(int arr[], int n, int target) {
          for (int i = 0; i < n; i++) {
            if (arr[i] == target) return i;
          }
          return -1;
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.space?.spaceComplexity).toBe('O(1)');
      expect(result.space?.details.variables).toBeGreaterThan(0);
    });

    it('should detect linear space from recursion', () => {
      const code = `
        int factorial(int n) {
          if (n <= 1) return 1;
          return n * factorial(n - 1);
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.space?.spaceComplexity).toBe('O(n)');
      expect(result.space?.details.recursionDepth).toBe(1);
    });

    it('should detect data structures', () => {
      const code = `
        void example() {
          vector<int> arr;
          map<string, int> lookup;
          set<int> visited;
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'cpp');
      expect(result.space?.details.dataStructures.length).toBeGreaterThan(0);
      expect(result.space?.details.dataStructures).toContain('vector');
    });

    it('should analyze Python list comprehensions', () => {
      const code = `
        def process_data(data):
          result = [x * 2 for x in data]
          return result
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'python');
      expect(result.space?.spaceComplexity).toBe('O(n)');
    });

    it('should detect JavaScript array methods', () => {
      const code = `
        function processArray(arr) {
          const doubled = arr.map(x => x * 2);
          return doubled.filter(x => x > 10);
        }
      `;
      const result = enhancedComplexityAnalyzer.analyzeCode(code, 'javascript');
      expect(result.space?.spaceComplexity).toBe('O(n)');
    });
  });
});
