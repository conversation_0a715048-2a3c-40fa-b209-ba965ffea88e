import React from 'react';
import { Code, Zap } from 'lucide-react';
import { SupportedLanguage } from '../types/analysis';

interface Example {
  name: string;
  language: SupportedLanguage;
  code: string;
  complexity: string;
}

const examples: Example[] = [
  {
    name: 'Linear Search',
    language: 'cpp',
    code: `int linearSearch(int arr[], int n, int target) {
    for (int i = 0; i < n; i++) {
        if (arr[i] == target) {
            return i;
        }
    }
    return -1;
}`,
    complexity: 'O(n)'
  },
  {
    name: 'Bubble Sort',
    language: 'cpp',
    code: `void bubbleSort(int arr[], int n) {
    for (int i = 0; i < n - 1; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}`,
    complexity: 'O(n²)'
  },
  {
    name: 'Binary Search',
    language: 'cpp',
    code: `int binarySearch(int arr[], int l, int r, int x) {
    if (r >= l) {
        int mid = l + (r - l) / 2;
        if (arr[mid] == x)
            return mid;
        if (arr[mid] > x)
            return binarySearch(arr, l, mid - 1, x);
        return binarySearch(arr, mid + 1, r, x);
    }
    return -1;
}`,
    complexity: 'O(log n)'
  },
  {
    name: 'Fibonacci Recursive',
    language: 'cpp',
    code: `int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}`,
    complexity: 'O(2^n)'
  },
  {
    name: 'Selection Sort',
    language: 'cpp',
    code: `void selectionSort(int arr[], int n) {
    for (int i = 0; i < n - 1; i++) {
        int min_idx = i;
        for (int j = i + 1; j < n; j++) {
            if (arr[j] < arr[min_idx]) {
                min_idx = j;
            }
        }
        int temp = arr[min_idx];
        arr[min_idx] = arr[i];
        arr[i] = temp;
    }
}`,
    complexity: 'O(n²)'
  },
  {
    name: 'Matrix Multiplication',
    language: 'cpp',
    code: `void matrixMultiply(int A[][N], int B[][N], int C[][N]) {
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < N; j++) {
            C[i][j] = 0;
            for (int k = 0; k < N; k++) {
                C[i][j] += A[i][k] * B[k][j];
            }
        }
    }
}`,
    complexity: 'O(n³)'
  },

  {
    name: 'Factorial Recursion',
    language: 'cpp',
    code: `int factorial(int n) {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);  // O(n) space due to call stack
}`,
    complexity: 'O(n) time, O(n) space'
  }
];

interface ExampleSelectorProps {
  onSelectExample: (code: string, language: SupportedLanguage) => void;
}

export const ExampleSelector: React.FC<ExampleSelectorProps> = ({ onSelectExample }) => {
  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
      <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
        <Zap className="w-5 h-5 mr-2 text-yellow-400" />
        Test Examples
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {examples.map((example, index) => (
          <button
            key={index}
            onClick={() => onSelectExample(example.code, example.language)}
            className="flex items-center justify-between p-3 bg-gray-900/50 hover:bg-gray-900 rounded-lg border border-gray-700 hover:border-gray-600 transition-all group"
          >
            <div className="flex items-center space-x-3">
              <Code className="w-4 h-4 text-blue-400" />
              <div className="text-left">
                <div className="text-white font-medium">{example.name}</div>
                <div className="text-xs text-gray-400">{example.language}</div>
              </div>
            </div>
            <div className="text-xs font-mono px-2 py-1 bg-gray-800 rounded text-blue-400 group-hover:text-blue-300">
              {example.complexity}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};