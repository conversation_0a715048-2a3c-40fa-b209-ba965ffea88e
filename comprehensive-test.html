<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive Complexity Analysis Test</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .pass { background-color: #d4edda; }
        .fail { background-color: #f8d7da; }
        .result { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Comprehensive Complexity Analysis Test</h1>
    <div id="results"></div>
    
    <script type="module">
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const testCases = [
            {
                name: "Sequential Loops (Merge Sort)",
                code: `void merge(int arr[], int l, int m, int r) {
    int n1 = m - l + 1;
    int n2 = r - m;
    int L[n1], R[n2];
    
    for (int i = 0; i < n1; i++)
        L[i] = arr[l + i];
    for (int j = 0; j < n2; j++)
        R[j] = arr[m + 1 + j];
}`,
                expectedTime: "O(n)",
                expectedSpace: "O(n)"
            },
            {
                name: "Nested Loops (Bubble Sort)",
                code: `void bubbleSort(int arr[], int n) {
    for (int i = 0; i < n-1; i++) {
        for (int j = 0; j < n-i-1; j++) {
            if (arr[j] > arr[j+1]) {
                swap(arr[j], arr[j+1]);
            }
        }
    }
}`,
                expectedTime: "O(n^2)",
                expectedSpace: "O(1)"
            },
            {
                name: "Fibonacci (Exponential Recursion)",
                code: `int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}`,
                expectedTime: "O(2^n)",
                expectedSpace: "O(n)"
            },
            {
                name: "Linear Search (Single Loop)",
                code: `int linearSearch(int arr[], int n, int x) {
    for (int i = 0; i < n; i++) {
        if (arr[i] == x)
            return i;
    }
    return -1;
}`,
                expectedTime: "O(n)",
                expectedSpace: "O(1)"
            },
            {
                name: "Binary Search (Logarithmic)",
                code: `int binarySearch(int arr[], int l, int r, int x) {
    if (r >= l) {
        int mid = l + (r - l) / 2;
        if (arr[mid] == x)
            return mid;
        if (arr[mid] > x)
            return binarySearch(arr, l, mid - 1, x);
        return binarySearch(arr, mid + 1, r, x);
    }
    return -1;
}`,
                expectedTime: "O(log n)",
                expectedSpace: "O(log n)"
            }
        ];

        function runTests() {
            const parser = new CppParser();
            const results = [];
            
            testCases.forEach(testCase => {
                try {
                    const result = parser.parse(testCase.code);
                    const timePass = result.complexity === testCase.expectedTime;
                    const spacePass = result.space?.spaceComplexity === testCase.expectedSpace;
                    
                    results.push({
                        name: testCase.name,
                        timeComplexity: result.complexity,
                        expectedTime: testCase.expectedTime,
                        timePass,
                        spaceComplexity: result.space?.spaceComplexity || 'N/A',
                        expectedSpace: testCase.expectedSpace,
                        spacePass,
                        confidence: result.confidence,
                        patterns: result.details.patterns,
                        explanation: result.explanation
                    });
                } catch (error) {
                    results.push({
                        name: testCase.name,
                        error: error.message,
                        timePass: false,
                        spacePass: false
                    });
                }
            });
            
            displayResults(results);
        }

        function displayResults(results) {
            const container = document.getElementById('results');
            let html = '';
            
            results.forEach(result => {
                const overallPass = result.timePass && result.spacePass;
                const cssClass = overallPass ? 'pass' : 'fail';
                
                html += `
                    <div class="test-case ${cssClass}">
                        <h3>${result.name} ${overallPass ? '✅' : '❌'}</h3>
                        
                        <div class="result">
                            <strong>Time Complexity:</strong> 
                            ${result.timeComplexity} 
                            (Expected: ${result.expectedTime}) 
                            ${result.timePass ? '✅' : '❌'}
                        </div>
                        
                        <div class="result">
                            <strong>Space Complexity:</strong> 
                            ${result.spaceComplexity} 
                            (Expected: ${result.expectedSpace}) 
                            ${result.spacePass ? '✅' : '❌'}
                        </div>
                        
                        <div class="result">
                            <strong>Confidence:</strong> ${result.confidence}%
                        </div>
                        
                        <div class="result">
                            <strong>Explanation:</strong> ${result.explanation}
                        </div>
                        
                        ${result.patterns ? `
                            <div class="result">
                                <strong>Patterns:</strong>
                                <ul>
                                    ${result.patterns.map(p => `<li>${p}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                        
                        ${result.error ? `
                            <div class="result" style="color: red;">
                                <strong>Error:</strong> ${result.error}
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            // Add summary
            const totalTests = results.length;
            const passedTests = results.filter(r => r.timePass && r.spacePass).length;
            
            html = `
                <div class="test-case ${passedTests === totalTests ? 'pass' : 'fail'}">
                    <h2>Test Summary: ${passedTests}/${totalTests} Passed</h2>
                </div>
            ` + html;
            
            container.innerHTML = html;
        }

        // Run tests when page loads
        runTests();
    </script>
</body>
</html>
