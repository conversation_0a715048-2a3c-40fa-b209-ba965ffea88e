import { ComplexityResult, SupportedLanguage } from '../types/analysis';

class ComplexityAnalyzer {
  analyzeCode(code: string, language: SupportedLanguage): ComplexityResult {
    const cleanCode = this.cleanCode(code);
    
    switch (language) {
      case 'python':
        return this.analyzePython(cleanCode);
      case 'cpp':
        return this.analyzeCpp(cleanCode);
      case 'javascript':
        return this.analyzeJavaScript(cleanCode);
      default:
        return this.getDefaultResult();
    }
  }

  private cleanCode(code: string): string {
    // Remove comments and empty lines
    return code
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && !line.startsWith('#') && !line.startsWith('//'))
      .join('\n');
  }

  private analyzePython(code: string): ComplexityResult {
    const patterns: string[] = [];
    let nestingLevel = 0;
    let maxNesting = 0;
    let loopCount = 0;
    let recursiveCount = 0;

    const lines = code.split('\n');
    const functionName = this.extractFunctionName(code, 'python');

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Count loops
      if (this.isLoop(trimmedLine, 'python')) {
        loopCount++;
        nestingLevel++;
        maxNesting = Math.max(maxNesting, nestingLevel);
        patterns.push(`Loop detected: ${trimmedLine}`);
      }
      
      // Check for recursion
      if (functionName && trimmedLine.includes(functionName + '(')) {
        recursiveCount++;
        patterns.push(`Recursive call: ${trimmedLine}`);
      }
      
      // Track indentation changes (rough nesting estimation)
      const indentLevel = (line.match(/^ */)?.[0].length || 0) / 4;
      if (indentLevel < nestingLevel) {
        nestingLevel = indentLevel;
      }
    }

    return this.calculateComplexity(loopCount, maxNesting, recursiveCount, patterns);
  }

  private analyzeCpp(code: string): ComplexityResult {
    const patterns: string[] = [];
    let nestingLevel = 0;
    let maxNesting = 0;
    let loopCount = 0;
    let recursiveCount = 0;

    const lines = code.split('\n');
    const functionName = this.extractFunctionName(code, 'cpp');

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Count braces for nesting
      const openBraces = (trimmedLine.match(/{/g) || []).length;
      const closeBraces = (trimmedLine.match(/}/g) || []).length;
      nestingLevel += openBraces - closeBraces;
      maxNesting = Math.max(maxNesting, nestingLevel);
      
      // Count loops
      if (this.isLoop(trimmedLine, 'cpp')) {
        loopCount++;
        patterns.push(`Loop detected: ${trimmedLine}`);
      }
      
      // Check for recursion
      if (functionName && trimmedLine.includes(functionName + '(')) {
        recursiveCount++;
        patterns.push(`Recursive call: ${trimmedLine}`);
      }
    }

    return this.calculateComplexity(loopCount, maxNesting, recursiveCount, patterns);
  }

  private analyzeJavaScript(code: string): ComplexityResult {
    const patterns: string[] = [];
    let nestingLevel = 0;
    let maxNesting = 0;
    let loopCount = 0;
    let recursiveCount = 0;

    const lines = code.split('\n');
    const functionName = this.extractFunctionName(code, 'javascript');

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Count braces for nesting
      const openBraces = (trimmedLine.match(/{/g) || []).length;
      const closeBraces = (trimmedLine.match(/}/g) || []).length;
      nestingLevel += openBraces - closeBraces;
      maxNesting = Math.max(maxNesting, nestingLevel);
      
      // Count loops
      if (this.isLoop(trimmedLine, 'javascript')) {
        loopCount++;
        patterns.push(`Loop detected: ${trimmedLine}`);
      }
      
      // Check for recursion
      if (functionName && trimmedLine.includes(functionName + '(')) {
        recursiveCount++;
        patterns.push(`Recursive call: ${trimmedLine}`);
      }
    }

    return this.calculateComplexity(loopCount, maxNesting, recursiveCount, patterns);
  }

  private isLoop(line: string, language: SupportedLanguage): boolean {
    switch (language) {
      case 'python':
        return /^(for|while)\s/.test(line);
      case 'cpp':
        return /^(for|while)\s*\(/.test(line);
      case 'javascript':
        return /^(for|while)\s*\(/.test(line) || /\.forEach\s*\(/.test(line) || /\.map\s*\(/.test(line);
      default:
        return false;
    }
  }

  private extractFunctionName(code: string, language: SupportedLanguage): string | null {
    let match;
    
    switch (language) {
      case 'python':
        match = code.match(/def\s+(\w+)\s*\(/);
        break;
      case 'cpp':
        match = code.match(/(?:int|void|double|float|char|string)\s+(\w+)\s*\(/);
        break;
      case 'javascript':
        match = code.match(/function\s+(\w+)\s*\(/) || code.match(/const\s+(\w+)\s*=/) || code.match(/(\w+)\s*=\s*\(/);
        break;
      default:
        return null;
    }
    
    return match ? match[1] : null;
  }

  private calculateComplexity(loopCount: number, maxNesting: number, recursiveCount: number, patterns: string[]): ComplexityResult {
    let complexity = 'O(1)';
    let explanation = 'Constant time - no loops or recursive calls detected.';
    let confidence = 95;

    if (recursiveCount > 0) {
      complexity = 'O(n)';
      explanation = 'Linear time due to recursive calls. Each recursive call processes one element.';
      confidence = 80;
      
      if (recursiveCount > 1 || patterns.some(p => p.includes('recursive call') && p.includes('+'))) {
        complexity = 'O(2^n)';
        explanation = 'Exponential time due to multiple recursive calls or branching recursion.';
        confidence = 75;
      }
    } else if (maxNesting >= 3) {
      complexity = 'O(n^3)';
      explanation = `Cubic time due to ${maxNesting} levels of nested loops.`;
      confidence = 85;
    } else if (maxNesting === 2) {
      complexity = 'O(n^2)';
      explanation = 'Quadratic time due to two nested loops.';
      confidence = 90;
    } else if (loopCount > 0 || maxNesting === 1) {
      complexity = 'O(n)';
      explanation = loopCount > 1 ? 
        'Linear time due to sequential loops.' : 
        'Linear time due to a single loop.';
      confidence = 90;
    }

    // Adjust confidence based on code complexity
    if (patterns.length > 5) {
      confidence -= 10;
    }

    return {
      complexity,
      explanation,
      confidence,
      details: {
        loops: loopCount,
        nestingLevel: maxNesting,
        recursiveCalls: recursiveCount,
        patterns
      }
    };
  }

  private getDefaultResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Unable to analyze the provided code.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: []
      }
    };
  }
}

export const complexityAnalyzer = new ComplexityAnalyzer();