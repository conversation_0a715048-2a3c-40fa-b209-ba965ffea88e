// Test script to verify complexity analysis improvements
const { enhancedComplexityAnalyzer } = require('./src/utils/enhancedComplexityAnalyzer');

// Test cases with expected results
const testCases = [
  {
    name: "Linear Search",
    code: `int linearSearch(int arr[], int n, int target) {
    for (int i = 0; i < n; i++) {
        if (arr[i] == target) {
            return i;
        }
    }
    return -1;
}`,
    language: "cpp",
    expected: "O(n)"
  },
  {
    name: "Binary Search",
    code: `int binarySearch(int arr[], int l, int r, int x) {
    if (r >= l) {
        int mid = l + (r - l) / 2;
        if (arr[mid] == x)
            return mid;
        if (arr[mid] > x)
            return binarySearch(arr, l, mid - 1, x);
        return binarySearch(arr, mid + 1, r, x);
    }
    return -1;
}`,
    language: "cpp",
    expected: "O(log n)"
  },
  {
    name: "Fibonacci Recursive",
    code: `int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + <PERSON><PERSON><PERSON><PERSON>(n - 2);
}`,
    language: "cpp",
    expected: "O(2^n)"
  },
  {
    name: "Bubble Sort",
    code: `void bubbleSort(int arr[], int n) {
    for (int i = 0; i < n - 1; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}`,
    language: "cpp",
    expected: "O(n^2)"
  }
];

console.log("Testing Complexity Analysis Improvements...\n");

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`Expected: ${testCase.expected}`);
  
  try {
    const result = enhancedComplexityAnalyzer.analyzeCode(testCase.code, testCase.language);
    console.log(`Actual: ${result.complexity}`);
    console.log(`Confidence: ${result.confidence}%`);
    console.log(`Explanation: ${result.explanation}`);
    console.log(`Details:`, result.details);
    
    const isCorrect = result.complexity === testCase.expected;
    console.log(`✅ ${isCorrect ? 'PASS' : 'FAIL'}`);
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }
  
  console.log("-".repeat(50));
});
