import { ComplexityResult } from '../../types/analysis';

interface CppNode {
  type: string;
  children?: CppNode[];
  value?: string;
  line?: number;
  depth?: number;
}

export class CppParser {
  private patterns: string[] = [];
  private loopCount = 0;
  private maxNesting = 0;
  private recursiveCount = 0;
  private functionName: string | null = null;
  private loopStack: number[] = []; // Track actual nesting depth

  parse(code: string): ComplexityResult {
    this.reset();
    
    try {
      // Use a more robust approach
      return this.analyzeWithBraceTracking(code);
    } catch (error) {
      console.warn('C++ analysis failed:', error);
      return this.getErrorResult();
    }
  }

  private reset() {
    this.patterns = [];
    this.loopCount = 0;
    this.maxNesting = 0;
    this.recursiveCount = 0;
    this.functionName = null;
    this.loopStack = [];
  }

  private analyzeWithBraceTracking(code: string): ComplexityResult {
    // Remove comments first
    const cleanCode = code
      .replace(/\/\/.*$/gm, '')
      .replace(/\/\*[\s\S]*?\*\//g, '');

    const lines = cleanCode.split('\n');
    let braceDepth = 0;
    let currentLoopDepth = 0;
    
    // Extract function name
    const funcMatch = cleanCode.match(/(?:int|void|double|float|char|string|bool)\s+(\w+)\s*\(/);
    this.functionName = funcMatch ? funcMatch[1] : null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Track braces to understand scope depth
      const openBraces = (line.match(/{/g) || []).length;
      const closeBraces = (line.match(/}/g) || []).length;
      
      // Check for loops BEFORE updating brace depth
      if (this.isLoopLine(line)) {
        this.loopCount++;
        currentLoopDepth++;
        this.maxNesting = Math.max(this.maxNesting, currentLoopDepth);
        this.patterns.push(`Loop at depth ${currentLoopDepth}: ${line}`);
        
        // If this loop has an opening brace on the same line, track it
        if (openBraces > 0) {
          this.loopStack.push(braceDepth + 1);
        } else {
          // Look ahead for the opening brace
          for (let j = i + 1; j < lines.length; j++) {
            const nextLine = lines[j].trim();
            if (nextLine === '{') {
              this.loopStack.push(braceDepth + 1);
              break;
            } else if (nextLine && !nextLine.startsWith('//')) {
              // Single statement loop
              break;
            }
          }
        }
      }

      // Update brace depth
      braceDepth += openBraces - closeBraces;

      // Check if we're exiting a loop scope
      while (this.loopStack.length > 0 && braceDepth < this.loopStack[this.loopStack.length - 1]) {
        this.loopStack.pop();
        currentLoopDepth--;
      }

      // Check for recursion
      if (this.functionName && line.includes(this.functionName + '(')) {
        this.recursiveCount++;
        this.patterns.push(`Recursive call: ${line}`);
      }
    }

    return this.calculateComplexity();
  }

  private isLoopLine(line: string): boolean {
    // More precise loop detection
    return /^\s*(for|while)\s*\(/.test(line) || 
           /^\s*for\s*\(/.test(line) || 
           /^\s*while\s*\(/.test(line);
  }

  private calculateComplexity(): ComplexityResult {
    let complexity = 'O(1)';
    let explanation = 'Constant time - no loops or recursive calls detected.';
    let confidence = 95;

    // Debug info
    console.log('Analysis Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      recursiveCount: this.recursiveCount,
      patterns: this.patterns
    });

    if (this.recursiveCount > 0) {
      // Check for divide and conquer (like binary search)
      const isDivideConquer = this.patterns.some(p => 
        p.includes('Recursive call') && this.recursiveCount <= 2
      );
      
      if (isDivideConquer && this.recursiveCount <= 2) {
        complexity = 'O(log n)';
        explanation = 'Logarithmic time due to divide-and-conquer recursion (like binary search).';
        confidence = 85;
      } else if (this.recursiveCount > 1) {
        complexity = 'O(2^n)';
        explanation = 'Exponential time due to multiple recursive calls (like naive Fibonacci).';
        confidence = 80;
      } else {
        complexity = 'O(n)';
        explanation = 'Linear time due to single recursive calls processing each element.';
        confidence = 90;
      }
    } else if (this.maxNesting >= 3) {
      complexity = 'O(n^3)';
      explanation = `Cubic time complexity due to ${this.maxNesting} levels of nested loops.`;
      confidence = 95;
    } else if (this.maxNesting >= 2) {
      complexity = 'O(n^2)';
      explanation = 'Quadratic time complexity due to nested loops (like bubble sort, selection sort).';
      confidence = 95;
    } else if (this.loopCount > 0) {
      complexity = 'O(n)';
      explanation = this.loopCount > 1 ? 
        'Linear time due to sequential loops (not nested).' : 
        'Linear time due to a single loop.';
      confidence = 95;
    }

    return {
      complexity,
      explanation,
      confidence: Math.max(confidence, 70),
      details: {
        loops: this.loopCount,
        nestingLevel: this.maxNesting,
        recursiveCalls: this.recursiveCount,
        patterns: this.patterns
      }
    };
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Analysis failed. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }
}