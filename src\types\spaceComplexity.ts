export interface SpaceComplexityResult {
  spaceComplexity: string;
  explanation: string;
  confidence: number;
  details: {
    variables: number;
    dataStructures: string[];
    recursionDepth: number;
    auxiliarySpace: string;
    patterns: string[];
  };
}

export interface EnhancedComplexityResult {
  time: {
    complexity: string;
    explanation: string;
    confidence: number;
    bestCase?: string;
    averageCase?: string;
    worstCase?: string;
  };
  space: SpaceComplexityResult;
  details: {
    loops: number;
    nestingLevel: number;
    recursiveCalls: number;
    patterns: string[];
    algorithmType?: string;
    optimizationSuggestions: string[];
  };
}
