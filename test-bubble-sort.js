// Quick test for bubble sort complexity detection
import { CppParser } from './src/utils/parsers/cppParser.js';

const bubbleSortCode = `void bubbleSort(int arr[], int n) {
    for (int i = 0; i < n - 1; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}`;

const parser = new CppParser();
const result = parser.parse(bubbleSortCode);

console.log('Bubble Sort Analysis:');
console.log('Complexity:', result.complexity);
console.log('Expected: O(n^2)');
console.log('Loops found:', result.details.loops);
console.log('Max nesting:', result.details.nestingLevel);
console.log('Patterns:', result.details.patterns);
console.log('Match:', result.complexity === 'O(n^2)' ? '✅ CORRECT' : '❌ INCORRECT');
