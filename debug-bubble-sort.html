<!DOCTYPE html>
<html>
<head>
    <title>Debug Bubble Sort Analysis</title>
</head>
<body>
    <h1>Debug Bubble Sort Analysis</h1>
    <pre id="output"></pre>
    
    <script type="module">
        // Import the parser directly
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const bubbleSortCode = `void bubbleSort(int arr[], int n) {
    for (int i = 0; i < n - 1; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}`;

        console.log('=== DEBUGGING BUBBLE SORT ===');
        console.log('Code to analyze:');
        console.log(bubbleSortCode);
        
        try {
            const parser = new CppParser();
            const result = parser.parse(bubbleSortCode);
            
            console.log('\n=== ANALYSIS RESULT ===');
            console.log('Complexity:', result.complexity);
            console.log('Expected: O(n^2)');
            console.log('Loops found:', result.details.loops);
            console.log('Max nesting:', result.details.nestingLevel);
            console.log('Patterns:', result.details.patterns);
            console.log('Confidence:', result.confidence);
            console.log('Explanation:', result.explanation);
            
            const output = document.getElementById('output');
            output.textContent = `
Bubble Sort Analysis Results:
============================
Complexity: ${result.complexity}
Expected: O(n^2)
Loops found: ${result.details.loops}
Max nesting: ${result.details.nestingLevel}
Confidence: ${result.confidence}%
Explanation: ${result.explanation}

Patterns detected:
${result.details.patterns.join('\n')}

Status: ${result.complexity === 'O(n^2)' ? '✅ CORRECT' : '❌ INCORRECT - Still showing ' + result.complexity}
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
