<!DOCTYPE html>
<html>
<head>
    <title>Test Merge Sort Fix</title>
</head>
<body>
    <h1>Test Merge Sort Sequential Loops Fix</h1>
    <pre id="output"></pre>
    
    <script type="module">
        // Import the parser directly
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const mergeCode = `void merge(int arr[], int l, int m, int r) {
    int n1 = m - l + 1;
    int n2 = r - m;
    int L[n1], R[n2];
    
    for (int i = 0; i < n1; i++)
        L[i] = arr[l + i];
    for (int j = 0; j < n2; j++)
        R[j] = arr[m + 1 + j];
        
    // Merge logic...
}`;

        const nestedLoopCode = `void bubbleSort(int arr[], int n) {
    for (int i = 0; i < n-1; i++) {
        for (int j = 0; j < n-i-1; j++) {
            if (arr[j] > arr[j+1]) {
                swap(arr[j], arr[j+1]);
            }
        }
    }
}`;

        console.log('=== TESTING MERGE SORT FIX ===');
        
        try {
            const parser = new CppParser();
            
            // Test 1: Merge function (sequential loops)
            console.log('\n=== TEST 1: MERGE FUNCTION (SEQUENTIAL LOOPS) ===');
            const mergeResult = parser.parse(mergeCode);
            console.log('Time Complexity:', mergeResult.complexity);
            console.log('Expected: O(n)');
            console.log('Patterns:', mergeResult.details.patterns);
            
            // Test 2: Bubble sort (nested loops)
            console.log('\n=== TEST 2: BUBBLE SORT (NESTED LOOPS) ===');
            const bubbleResult = parser.parse(nestedLoopCode);
            console.log('Time Complexity:', bubbleResult.complexity);
            console.log('Expected: O(n^2)');
            console.log('Patterns:', bubbleResult.details.patterns);
            
            const output = document.getElementById('output');
            output.textContent = `
Merge Sort Sequential Loops Fix Test Results:
============================================

TEST 1: MERGE FUNCTION (Sequential Loops)
Time Complexity: ${mergeResult.complexity}
Expected: O(n)
Status: ${mergeResult.complexity === 'O(n)' ? '✅ FIXED!' : '❌ Still broken'}

Loops: ${mergeResult.details.loopCount}
Max Nesting: ${mergeResult.details.maxNesting}
Confidence: ${mergeResult.confidence}%

Patterns detected:
${mergeResult.details.patterns.join('\n')}

---

TEST 2: BUBBLE SORT (Nested Loops)
Time Complexity: ${bubbleResult.complexity}
Expected: O(n^2)
Status: ${bubbleResult.complexity === 'O(n^2)' ? '✅ CORRECT' : '❌ Broken'}

Loops: ${bubbleResult.details.loopCount}
Max Nesting: ${bubbleResult.details.maxNesting}
Confidence: ${bubbleResult.confidence}%

Patterns detected:
${bubbleResult.details.patterns.join('\n')}

---

SUMMARY:
Sequential loops (merge): ${mergeResult.complexity === 'O(n)' ? '✅' : '❌'}
Nested loops (bubble): ${bubbleResult.complexity === 'O(n^2)' ? '✅' : '❌'}
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
