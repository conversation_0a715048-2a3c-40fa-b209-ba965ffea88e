import React from 'react';
import { History, TrendingUp, Clock } from 'lucide-react';
import { ComplexityResult } from '../types/analysis';

interface AnalysisHistoryProps {
  history: ComplexityResult[];
  onSelectResult?: (result: ComplexityResult) => void;
  className?: string;
}

const complexityColors = {
  'O(1)': 'text-green-400 bg-green-400/10',
  'O(log n)': 'text-green-400 bg-green-400/10',
  'O(n)': 'text-yellow-400 bg-yellow-400/10',
  'O(n log n)': 'text-orange-400 bg-orange-400/10',
  'O(n^2)': 'text-red-400 bg-red-400/10',
  'O(n^3)': 'text-red-500 bg-red-500/10',
  'O(2^n)': 'text-purple-400 bg-purple-400/10',
  'O(?)': 'text-gray-400 bg-gray-400/10'
};

export const AnalysisHistory: React.FC<AnalysisHistoryProps> = ({
  history,
  onSelectResult,
  className = ''
}) => {
  if (history.length === 0) {
    return (
      <div className={`bg-gray-800 rounded-lg p-4 border border-gray-700 ${className}`}>
        <div className="text-center text-gray-400">
          <History className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Analysis history will appear here</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-800 rounded-lg p-4 border border-gray-700 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
        <History className="w-5 h-5 mr-2 text-blue-400" />
        Recent Analysis
      </h3>
      
      <div className="space-y-2">
        {history.map((result, index) => {
          const complexityStyle = complexityColors[result.complexity as keyof typeof complexityColors] || complexityColors['O(?)'];
          
          return (
            <div
              key={index}
              onClick={() => onSelectResult?.(result)}
              className={`p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-all cursor-pointer group ${
                index === 0 ? 'ring-1 ring-blue-500/20 bg-blue-500/5' : 'hover:bg-gray-900/50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`px-2 py-1 rounded font-mono text-sm font-bold ${complexityStyle}`}>
                    {result.complexity}
                  </div>
                  <div className="text-sm text-gray-300 group-hover:text-white transition-colors">
                    {result.details.loops} loops, {result.details.nestingLevel} max nesting
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="text-xs text-gray-400">
                    {result.confidence}%
                  </div>
                  {index === 0 && (
                    <div className="px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300">
                      Current
                    </div>
                  )}
                </div>
              </div>
              
              <div className="mt-2 text-xs text-gray-400 line-clamp-2">
                {result.explanation}
              </div>
            </div>
          );
        })}
      </div>
      
      {history.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>Showing last {history.length} analyses</span>
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>Real-time updates</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};