import { useState, useEffect, useCallback, useRef } from 'react';
import { ComplexityResult, SupportedLanguage } from '../types/analysis';
import { enhancedComplexityAnalyzer } from '../utils/enhancedComplexityAnalyzer';

interface UseRealTimeAnalysisOptions {
  debounceMs?: number;
  minCodeLength?: number;
  enablePreview?: boolean;
}

export const useRealTimeAnalysis = (
  code: string,
  language: SupportedLanguage,
  options: UseRealTimeAnalysisOptions = {}
) => {
  const {
    debounceMs = 800,
    minCodeLength = 10,
    enablePreview = true
  } = options;

  const [result, setResult] = useState<ComplexityResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [previewResult, setPreviewResult] = useState<ComplexityResult | null>(null);
  const [analysisHistory, setAnalysisHistory] = useState<ComplexityResult[]>([]);
  
  const debounceTimerRef = useRef<NodeJS.Timeout>();
  const previewTimerRef = useRef<NodeJS.Timeout>();
  const lastAnalyzedCodeRef = useRef<string>('');

  // Fast preview analysis (less accurate but instant)
  const performPreviewAnalysis = useCallback((codeToAnalyze: string) => {
    if (!enablePreview || codeToAnalyze.length < minCodeLength) {
      setPreviewResult(null);
      return;
    }

    try {
      // Quick pattern-based analysis for preview
      const quickResult = enhancedComplexityAnalyzer.analyzeCode(codeToAnalyze, language);
      setPreviewResult({
        ...quickResult,
        confidence: Math.max(quickResult.confidence - 20, 30), // Lower confidence for preview
        explanation: `Preview: ${quickResult.explanation}`
      });
    } catch (error) {
      console.warn('Preview analysis failed:', error);
      setPreviewResult(null);
    }
  }, [language, minCodeLength, enablePreview]);

  // Full analysis with higher accuracy
  const performFullAnalysis = useCallback(async (codeToAnalyze: string) => {
    if (codeToAnalyze.length < minCodeLength) {
      setResult(null);
      setIsAnalyzing(false);
      return;
    }

    if (codeToAnalyze === lastAnalyzedCodeRef.current) {
      setIsAnalyzing(false);
      return;
    }

    setIsAnalyzing(true);
    
    try {
      // Simulate processing time for better UX
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const analysisResult = enhancedComplexityAnalyzer.analyzeCode(codeToAnalyze, language);
      
      setResult(analysisResult);
      setPreviewResult(null); // Clear preview when full result is ready
      
      // Add to history
      setAnalysisHistory(prev => {
        const newHistory = [analysisResult, ...prev.slice(0, 4)]; // Keep last 5 results
        return newHistory;
      });
      
      lastAnalyzedCodeRef.current = codeToAnalyze;
    } catch (error) {
      console.error('Full analysis failed:', error);
      setResult({
        complexity: 'O(?)',
        explanation: 'Analysis failed. Please check your code syntax.',
        confidence: 0,
        details: {
          loops: 0,
          nestingLevel: 0,
          recursiveCalls: 0,
          patterns: ['Analysis error']
        }
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [language, minCodeLength]);

  // Real-time analysis effect
  useEffect(() => {
    // Clear existing timers
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    if (previewTimerRef.current) {
      clearTimeout(previewTimerRef.current);
    }

    // Immediate preview for quick feedback
    if (enablePreview) {
      previewTimerRef.current = setTimeout(() => {
        performPreviewAnalysis(code);
      }, 150); // Very fast preview
    }

    // Debounced full analysis
    debounceTimerRef.current = setTimeout(() => {
      performFullAnalysis(code);
    }, debounceMs);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      if (previewTimerRef.current) {
        clearTimeout(previewTimerRef.current);
      }
    };
  }, [code, language, debounceMs, performFullAnalysis, performPreviewAnalysis, enablePreview]);

  // Manual trigger for immediate analysis
  const triggerAnalysis = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    performFullAnalysis(code);
  }, [code, performFullAnalysis]);

  // Clear analysis
  const clearAnalysis = useCallback(() => {
    setResult(null);
    setPreviewResult(null);
    setIsAnalyzing(false);
    lastAnalyzedCodeRef.current = '';
  }, []);

  return {
    result,
    previewResult,
    isAnalyzing,
    analysisHistory,
    triggerAnalysis,
    clearAnalysis,
    hasPreview: !!previewResult,
    isRealTimeEnabled: code.length >= minCodeLength
  };
};