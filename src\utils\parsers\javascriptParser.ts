import { ComplexityResult } from '../../types/analysis';

export class JavaScriptParser {
  private patterns: string[] = [];
  private loopCount = 0;
  private maxNesting = 0;
  private recursiveCount = 0;
  private functionName: string | null = null;

  parse(code: string): ComplexityResult {
    this.reset();
    
    try {
      return this.analyzeWithBraceTracking(code);
    } catch (error) {
      console.warn('JavaScript analysis failed:', error);
      return this.getErrorResult();
    }
  }

  private reset() {
    this.patterns = [];
    this.loopCount = 0;
    this.maxNesting = 0;
    this.recursiveCount = 0;
    this.functionName = null;
  }

  private analyzeWithBraceTracking(code: string): ComplexityResult {
    // Remove comments
    const cleanCode = code
      .replace(/\/\/.*$/gm, '')
      .replace(/\/\*[\s\S]*?\*\//g, '');

    const lines = cleanCode.split('\n');
    let braceDepth = 0;
    const activeLoops: number[] = []; // Stack of brace depths where loops start

    // Extract function name
    const funcMatch = cleanCode.match(/function\s+(\w+)\s*\(/) ||
                     cleanCode.match(/const\s+(\w+)\s*=/) ||
                     cleanCode.match(/(\w+)\s*=\s*function/);
    this.functionName = funcMatch ? funcMatch[1] : null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Track braces
      const openBraces = (line.match(/{/g) || []).length;
      const closeBraces = (line.match(/}/g) || []).length;

      // Update brace depth FIRST
      braceDepth += openBraces - closeBraces;

      // Remove loops that have ended
      while (activeLoops.length > 0 && braceDepth < activeLoops[activeLoops.length - 1]) {
        activeLoops.pop();
      }

      // Check for loops AFTER updating scope
      if (this.isLoopLine(line)) {
        this.loopCount++;
        activeLoops.push(braceDepth); // Current depth where loop starts
        const currentNesting = activeLoops.length;
        this.maxNesting = Math.max(this.maxNesting, currentNesting);
        this.patterns.push(`Loop at nesting level ${currentNesting}: ${line}`);
      }

      // Check for array methods that are loops
      if (this.isArrayMethod(line)) {
        this.loopCount++;
        activeLoops.push(braceDepth);
        const currentNesting = activeLoops.length;
        this.maxNesting = Math.max(this.maxNesting, currentNesting);
        this.patterns.push(`Array method at nesting level ${currentNesting}: ${line}`);
      }

      // Check for recursion (but not the function declaration itself)
      if (this.functionName && line.includes(this.functionName + '(') && !this.isFunctionDeclaration(line)) {
        // Count actual number of recursive calls in this line
        const callCount = this.countRecursiveCalls(line, this.functionName);
        this.recursiveCount += callCount;
        this.patterns.push(`Recursive call(s): ${line} (${callCount} calls)`);
      }
    }

    console.log('JavaScript Parser Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      patterns: this.patterns
    });

    return this.calculateComplexity();
  }

  private isLoopLine(line: string): boolean {
    const trimmed = line.trim();

    // Standard for loops
    if (/^for\s*\(/.test(trimmed)) return true;

    // While loops
    if (/^while\s*\(/.test(trimmed)) return true;

    // Do-while loops
    if (/^do\s*{/.test(trimmed)) return true;

    // For-in loops
    if (/^for\s*\(\s*\w+\s+in\s+/.test(trimmed)) return true;

    // For-of loops
    if (/^for\s*\(\s*\w+\s+of\s+/.test(trimmed)) return true;

    return false;
  }

  private isArrayMethod(line: string): boolean {
    // Common array methods that iterate
    return /\.(forEach|map|filter|reduce|find|some|every|findIndex)\s*\(/.test(line);
  }

  private isFunctionDeclaration(line: string): boolean {
    // Check if this line is a function declaration
    const trimmed = line.trim();
    return /^function\s+\w+\s*\(/.test(trimmed) ||
           /^const\s+\w+\s*=/.test(trimmed) ||
           /^\w+\s*=\s*function/.test(trimmed);
  }

  private countRecursiveCalls(line: string, functionName: string): number {
    // Count how many times the function is called in this line
    const regex = new RegExp(`\\b${functionName}\\s*\\(`, 'g');
    const matches = line.match(regex);
    return matches ? matches.length : 0;
  }

  private hasActualNestedLoops(): boolean {
    // Check if we actually have nested loops by analyzing the patterns more carefully
    const patterns = this.patterns.join('\n');
    const codeText = patterns.toLowerCase();

    // Count how many times we see each nesting level
    const level1Count = (patterns.match(/nesting level 1/g) || []).length;
    const level2Count = (patterns.match(/nesting level 2/g) || []).length;
    const level3Count = (patterns.match(/nesting level 3/g) || []).length;

    // Check for common nested loop keywords first (high confidence)
    const hasNestedKeywords = codeText.includes('bubble') ||
                             codeText.includes('selection') ||
                             codeText.includes('matrix') ||
                             codeText.includes('2d') ||
                             codeText.includes('nested');

    // If we have clear nested keywords, it's definitely nested
    if (hasNestedKeywords) {
      return true;
    }

    // True nesting means we have loops at level 2+
    const hasActualNesting = level2Count > 0 || level3Count > 0;

    // For merge sort case: if we have exactly 2 loops both at level 1, it's sequential
    // For bubble/selection sort: we should have 1 loop at level 1 and 1 at level 2
    const isSequentialPattern = (level1Count === 2 && level2Count === 0 && level3Count === 0);

    return hasActualNesting && !isSequentialPattern;
  }

  private calculateComplexity(): ComplexityResult {
    let complexity = 'O(1)';
    let explanation = 'Constant time - no loops or recursive calls detected.';
    let confidence = 95;

    console.log('JavaScript Analysis Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      recursiveCount: this.recursiveCount,
      patterns: this.patterns,
      functionName: this.functionName
    });

    if (this.recursiveCount > 0) {
      // Improved recursion analysis for JavaScript
      complexity = this.analyzeRecursiveComplexity();
      explanation = this.getRecursiveExplanation(complexity);
      confidence = this.getRecursiveConfidence(complexity);
    } else if (this.maxNesting >= 3) {
      complexity = 'O(n^3)';
      explanation = `Cubic time due to ${this.maxNesting} levels of nested loops.`;
      confidence = 95;
    } else if (this.maxNesting >= 2 && this.hasActualNestedLoops()) {
      complexity = 'O(n^2)';
      explanation = 'Quadratic time due to nested loops or nested array methods.';
      confidence = 95;
    } else if (this.loopCount > 0) {
      complexity = 'O(n)';
      explanation = this.loopCount > 1 ?
        'Linear time due to sequential loops or array methods.' :
        'Linear time due to a single loop or array method.';
      confidence = 95;
    }

    return {
      complexity,
      explanation,
      confidence: Math.max(confidence, 70),
      details: {
        loops: this.loopCount,
        nestingLevel: this.maxNesting,
        recursiveCalls: this.recursiveCount,
        patterns: this.patterns
      }
    };
  }

  private analyzeRecursiveComplexity(): string {
    // Check for binary search pattern
    if (this.isBinarySearchPattern()) {
      return 'O(log n)';
    }

    // Check for fibonacci-like pattern
    if (this.recursiveCount >= 2 && this.isFibonacciPattern()) {
      return 'O(2^n)';
    }

    // Single recursive call
    if (this.recursiveCount === 1) {
      return 'O(n)';
    }

    // Multiple recursive calls
    if (this.recursiveCount > 1) {
      return 'O(2^n)';
    }

    return 'O(n)';
  }

  private isBinarySearchPattern(): boolean {
    const patterns = this.patterns.join(' ').toLowerCase();
    return patterns.includes('mid') ||
           patterns.includes('left') && patterns.includes('right') ||
           patterns.includes('math.floor') ||
           patterns.includes('/ 2');
  }

  private isFibonacciPattern(): boolean {
    const patterns = this.patterns.join(' ').toLowerCase();
    const codeText = patterns;

    // Check for classic fibonacci patterns
    const hasFibonacciName = codeText.includes('fibonacci');
    const hasNMinus1And2 = codeText.includes('n - 1') && codeText.includes('n - 2');
    const hasTwoRecursiveCalls = this.recursiveCount >= 2;
    const hasReturnStatement = codeText.includes('return');

    // Strong indicators of fibonacci-like exponential recursion
    return hasFibonacciName ||
           (hasNMinus1And2 && hasTwoRecursiveCalls) ||
           (hasTwoRecursiveCalls && hasReturnStatement && codeText.includes('(2 calls)'));
  }

  private getRecursiveExplanation(complexity: string): string {
    switch (complexity) {
      case 'O(log n)':
        return 'Logarithmic time due to divide-and-conquer recursion (like binary search).';
      case 'O(2^n)':
        return 'Exponential time due to multiple recursive calls without memoization.';
      case 'O(n)':
        return 'Linear time due to recursive processing of each element.';
      default:
        return 'Recursive algorithm detected.';
    }
  }

  private getRecursiveConfidence(complexity: string): number {
    switch (complexity) {
      case 'O(log n)':
        return this.isBinarySearchPattern() ? 90 : 75;
      case 'O(2^n)':
        return this.isFibonacciPattern() ? 90 : 80;
      case 'O(n)':
        return 85;
      default:
        return 70;
    }
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Analysis failed. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }
}