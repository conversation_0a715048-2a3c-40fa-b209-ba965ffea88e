import { ComplexityResult } from '../../types/analysis';

export class JavaScriptParser {
  private patterns: string[] = [];
  private loopCount = 0;
  private maxNesting = 0;
  private recursiveCount = 0;
  private functionName: string | null = null;

  parse(code: string): ComplexityResult {
    this.reset();
    
    try {
      return this.analyzeWithBraceTracking(code);
    } catch (error) {
      console.warn('JavaScript analysis failed:', error);
      return this.getErrorResult();
    }
  }

  private reset() {
    this.patterns = [];
    this.loopCount = 0;
    this.maxNesting = 0;
    this.recursiveCount = 0;
    this.functionName = null;
  }

  private analyzeWithBraceTracking(code: string): ComplexityResult {
    // Remove comments
    const cleanCode = code
      .replace(/\/\/.*$/gm, '')
      .replace(/\/\*[\s\S]*?\*\//g, '');

    const lines = cleanCode.split('\n');
    let braceDepth = 0;
    let currentLoopDepth = 0;
    const loopStack: number[] = [];

    // Extract function name
    const funcMatch = cleanCode.match(/function\s+(\w+)\s*\(/) || 
                     cleanCode.match(/const\s+(\w+)\s*=/) || 
                     cleanCode.match(/(\w+)\s*=\s*function/);
    this.functionName = funcMatch ? funcMatch[1] : null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Track braces
      const openBraces = (line.match(/{/g) || []).length;
      const closeBraces = (line.match(/}/g) || []).length;

      // Check for loops BEFORE updating brace depth
      if (this.isLoopLine(line)) {
        this.loopCount++;
        currentLoopDepth++;
        this.maxNesting = Math.max(this.maxNesting, currentLoopDepth);
        this.patterns.push(`Loop at depth ${currentLoopDepth}: ${line}`);
        
        if (openBraces > 0) {
          loopStack.push(braceDepth + 1);
        } else {
          // Look for opening brace in next lines
          for (let j = i + 1; j < lines.length; j++) {
            const nextLine = lines[j].trim();
            if (nextLine === '{') {
              loopStack.push(braceDepth + 1);
              break;
            } else if (nextLine && !nextLine.startsWith('//')) {
              break;
            }
          }
        }
      }

      // Check for array methods that are loops
      if (this.isArrayMethod(line)) {
        this.loopCount++;
        currentLoopDepth++;
        this.maxNesting = Math.max(this.maxNesting, currentLoopDepth);
        this.patterns.push(`Array method at depth ${currentLoopDepth}: ${line}`);
      }

      // Update brace depth
      braceDepth += openBraces - closeBraces;

      // Check if we're exiting loop scopes
      while (loopStack.length > 0 && braceDepth < loopStack[loopStack.length - 1]) {
        loopStack.pop();
        currentLoopDepth--;
      }

      // Check for recursion
      if (this.functionName && line.includes(this.functionName + '(')) {
        this.recursiveCount++;
        this.patterns.push(`Recursive call: ${line}`);
      }
    }

    return this.calculateComplexity();
  }

  private isLoopLine(line: string): boolean {
    return /^\s*(for|while)\s*\(/.test(line);
  }

  private isArrayMethod(line: string): boolean {
    return /\.(forEach|map|filter|reduce|find|some|every)\s*\(/.test(line);
  }

  private calculateComplexity(): ComplexityResult {
    let complexity = 'O(1)';
    let explanation = 'Constant time - no loops or recursive calls detected.';
    let confidence = 95;

    console.log('JavaScript Analysis Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      recursiveCount: this.recursiveCount,
      patterns: this.patterns
    });

    if (this.recursiveCount > 0) {
      if (this.recursiveCount > 1) {
        complexity = 'O(2^n)';
        explanation = 'Exponential time due to multiple recursive calls.';
        confidence = 80;
      } else {
        complexity = 'O(n)';
        explanation = 'Linear time due to recursive processing.';
        confidence = 85;
      }
    } else if (this.maxNesting >= 3) {
      complexity = 'O(n^3)';
      explanation = `Cubic time due to ${this.maxNesting} levels of nested loops.`;
      confidence = 95;
    } else if (this.maxNesting >= 2) {
      complexity = 'O(n^2)';
      explanation = 'Quadratic time due to nested loops or nested array methods.';
      confidence = 95;
    } else if (this.loopCount > 0) {
      complexity = 'O(n)';
      explanation = this.loopCount > 1 ? 
        'Linear time due to sequential loops or array methods.' : 
        'Linear time due to a single loop or array method.';
      confidence = 95;
    }

    return {
      complexity,
      explanation,
      confidence: Math.max(confidence, 70),
      details: {
        loops: this.loopCount,
        nestingLevel: this.maxNesting,
        recursiveCalls: this.recursiveCount,
        patterns: this.patterns
      }
    };
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Analysis failed. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }
}