import React from 'react';
import { <PERSON>ap, Eye } from 'lucide-react';
import { ComplexityResult } from '../types/analysis';

interface PreviewBadgeProps {
  previewResult: ComplexityResult;
  className?: string;
}

const complexityColors = {
  'O(1)': 'border-green-400/30 bg-green-400/10 text-green-400',
  'O(log n)': 'border-green-400/30 bg-green-400/10 text-green-400',
  'O(n)': 'border-yellow-400/30 bg-yellow-400/10 text-yellow-400',
  'O(n log n)': 'border-orange-400/30 bg-orange-400/10 text-orange-400',
  'O(n^2)': 'border-red-400/30 bg-red-400/10 text-red-400',
  'O(n^3)': 'border-red-500/30 bg-red-500/10 text-red-500',
  'O(2^n)': 'border-purple-400/30 bg-purple-400/10 text-purple-400',
  'O(?)': 'border-gray-400/30 bg-gray-400/10 text-gray-400'
};

export const PreviewBadge: React.FC<PreviewBadgeProps> = ({
  previewResult,
  className = ''
}) => {
  const complexityStyle = complexityColors[previewResult.complexity as keyof typeof complexityColors] || complexityColors['O(?)'];

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${complexityStyle} ${className}`}>
      <div className="flex items-center space-x-1">
        <Eye className="w-4 h-4" />
        <span className="text-xs font-medium">Preview</span>
      </div>
      
      <div className="flex items-center space-x-2">
        <span className="font-mono font-bold text-sm">
          {previewResult.complexity}
        </span>
        
        <div className="flex items-center space-x-1">
          <Zap className="w-3 h-3" />
          <span className="text-xs">
            {previewResult.confidence}%
          </span>
        </div>
      </div>
    </div>
  );
};