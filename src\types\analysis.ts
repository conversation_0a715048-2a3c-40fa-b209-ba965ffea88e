export interface SpaceComplexityDetails {
  variables: number;
  dataStructures: string[];
  recursionDepth: number;
  auxiliarySpace: string;
  patterns: string[];
}

export interface SpaceComplexityResult {
  spaceComplexity: string;
  explanation: string;
  confidence: number;
  details: SpaceComplexityDetails;
}

export interface ComplexityResult {
  complexity: string;
  explanation: string;
  confidence: number;
  details: {
    loops: number;
    nestingLevel: number;
    recursiveCalls: number;
    patterns: string[];
  };
  space?: SpaceComplexityResult;
}

export interface EnhancedComplexityResult {
  time: ComplexityResult;
  space: SpaceComplexityResult;
  summary: {
    algorithmType?: string;
    optimizationSuggestions: string[];
    tradeoffs: string[];
  };
}

export type SupportedLanguage = 'python' | 'cpp' | 'javascript';

export interface AnalysisRequest {
  code: string;
  language: SupportedLanguage;
}