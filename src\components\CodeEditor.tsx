import React from 'react';
import Editor from '@monaco-editor/react';
import { SupportedLanguage } from '../types/analysis';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: SupportedLanguage;
}

const languageMap = {
  python: 'python',
  cpp: 'cpp',
  javascript: 'javascript'
};

export const CodeEditor: React.FC<CodeEditorProps> = ({ value, onChange, language }) => {
  const handleEditorChange = (newValue: string | undefined) => {
    onChange(newValue || '');
  };

  return (
    <div className="relative">
      <div className="absolute top-3 right-3 z-10 bg-gray-800 px-2 py-1 rounded text-xs text-gray-300">
        {language.toUpperCase()}
      </div>
      <Editor
        height="400px"
        language={languageMap[language]}
        value={value}
        onChange={handleEditorChange}
        theme="vs-dark"
        options={{
          minimap: { enabled: false },
          fontSize: 14,
          lineNumbers: 'on',
          roundedSelection: false,
          scrollBeyondLastLine: false,
          automaticLayout: true,
          tabSize: 2,
          insertSpaces: true,
          wordWrap: 'on',
          contextmenu: false,
          folding: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3,
          renderLineHighlight: 'line',
          selectionHighlight: false,
          cursorBlinking: 'smooth',
          cursorSmoothCaretAnimation: 'on'
        }}
        className="rounded-lg border border-gray-700 overflow-hidden"
      />
    </div>
  );
};