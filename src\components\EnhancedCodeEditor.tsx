import React, { useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { SupportedLanguage, ComplexityResult } from '../types/analysis';

interface EnhancedCodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: SupportedLanguage;
  result?: ComplexityResult | null;
}

export const EnhancedCodeEditor: React.FC<EnhancedCodeEditorProps> = ({
  value,
  onChange,
  language,
  result
}) => {
  const editorRef = useRef<any>(null);

  const getMonacoLanguage = (lang: SupportedLanguage): string => {
    switch (lang) {
      case 'cpp': return 'cpp';
      case 'python': return 'python';
      case 'javascript': return 'javascript';
      default: return 'plaintext';
    }
  };

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;
    
    // Configure editor theme
    monaco.editor.defineTheme('complexity-theme', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        // Custom tokens for complexity highlighting
        { token: 'complexity.loop', foreground: 'FFD700', fontStyle: 'bold' },
        { token: 'complexity.recursive', foreground: 'FF6B6B', fontStyle: 'bold' },
        { token: 'complexity.efficient', foreground: '4ECDC4', fontStyle: 'bold' },
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41',
      }
    });
    
    monaco.editor.setTheme('complexity-theme');
  };

  // Add complexity annotations to the editor
  useEffect(() => {
    if (editorRef.current && result) {
      const editor = editorRef.current;
      const model = editor.getModel();
      
      if (model) {
        // Clear existing decorations
        const oldDecorations = model.getAllDecorations();
        editor.deltaDecorations(oldDecorations.map(d => d.id), []);
        
        // Add new decorations based on analysis result
        const decorations = createComplexityDecorations(result, model);
        editor.deltaDecorations([], decorations);
        
        // Add hover providers for complexity information
        addComplexityHovers(editor, result);
      }
    }
  }, [result, value]);

  const createComplexityDecorations = (result: ComplexityResult, model: any) => {
    const decorations: any[] = [];
    const lines = model.getLinesContent();
    
    // Highlight loops
    result.details.patterns.forEach(pattern => {
      if (pattern.includes('Loop at nesting level')) {
        const lineNumber = findPatternLine(pattern, lines);
        if (lineNumber > 0) {
          decorations.push({
            range: new monaco.Range(lineNumber, 1, lineNumber, 1),
            options: {
              isWholeLine: true,
              className: 'complexity-loop-line',
              glyphMarginClassName: 'complexity-loop-glyph',
              hoverMessage: { value: `**Loop Detected**: ${pattern}` },
              minimap: {
                color: '#FFD700',
                position: 1
              }
            }
          });
        }
      }
    });
    
    // Highlight recursive calls
    if (result.details.recursiveCalls > 0) {
      result.details.patterns.forEach(pattern => {
        if (pattern.includes('Recursive call')) {
          const lineNumber = findPatternLine(pattern, lines);
          if (lineNumber > 0) {
            decorations.push({
              range: new monaco.Range(lineNumber, 1, lineNumber, 1),
              options: {
                isWholeLine: true,
                className: 'complexity-recursive-line',
                glyphMarginClassName: 'complexity-recursive-glyph',
                hoverMessage: { value: `**Recursive Call**: ${pattern}` },
                minimap: {
                  color: '#FF6B6B',
                  position: 1
                }
              }
            });
          }
        }
      });
    }
    
    return decorations;
  };

  const findPatternLine = (pattern: string, lines: string[]): number => {
    // Extract code snippet from pattern and find matching line
    const codeMatch = pattern.match(/: (.+)$/);
    if (codeMatch) {
      const code = codeMatch[1].trim();
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim().includes(code.substring(0, 20))) {
          return i + 1; // Monaco uses 1-based line numbers
        }
      }
    }
    return 0;
  };

  const addComplexityHovers = (editor: any, result: ComplexityResult) => {
    // Add complexity information on hover
    const hoverProvider = {
      provideHover: (model: any, position: any) => {
        const line = model.getLineContent(position.lineNumber);
        
        // Check if this line contains a loop or recursive call
        const isLoop = result.details.patterns.some(p => 
          p.includes('Loop') && line.trim().includes('for') || line.trim().includes('while')
        );
        
        const isRecursive = result.details.patterns.some(p => 
          p.includes('Recursive') && line.includes(p.split(':')[1]?.trim())
        );
        
        if (isLoop || isRecursive) {
          return {
            range: new monaco.Range(position.lineNumber, 1, position.lineNumber, line.length),
            contents: [
              { value: `**Complexity Impact**: ${result.complexity}` },
              { value: `**Confidence**: ${result.confidence}%` },
              { value: result.explanation }
            ]
          };
        }
        
        return null;
      }
    };
    
    monaco.languages.registerHoverProvider(getMonacoLanguage(language), hoverProvider);
  };

  return (
    <div className="relative">
      <Editor
        height="400px"
        language={getMonacoLanguage(language)}
        value={value}
        onChange={(val) => onChange(val || '')}
        onMount={handleEditorDidMount}
        options={{
          theme: 'complexity-theme',
          fontSize: 14,
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          wordWrap: 'on',
          lineNumbers: 'on',
          glyphMargin: true,
          folding: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3,
          renderLineHighlight: 'all',
          contextmenu: true,
          mouseWheelZoom: true,
        }}
      />
      
      {/* Complexity Legend */}
      {result && (
        <div className="absolute top-2 right-2 bg-gray-900/90 backdrop-blur-sm rounded-lg p-2 text-xs">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-yellow-400 rounded"></div>
              <span className="text-yellow-400">Loops</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-red-400 rounded"></div>
              <span className="text-red-400">Recursion</span>
            </div>
            <div className="text-white">
              {result.complexity}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
