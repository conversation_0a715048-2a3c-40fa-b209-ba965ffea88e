// Web Worker for background complexity analysis
import { enhancedComplexityAnalyzer } from '../utils/enhancedComplexityAnalyzer';
import { SupportedLanguage, ComplexityResult } from '../types/analysis';

export interface AnalysisMessage {
  id: string;
  code: string;
  language: SupportedLanguage;
  type: 'full' | 'preview';
}

export interface AnalysisResponse {
  id: string;
  result: ComplexityResult;
  type: 'full' | 'preview';
  error?: string;
}

// Handle messages from main thread
self.onmessage = async (event: MessageEvent<AnalysisMessage>) => {
  const { id, code, language, type } = event.data;
  
  try {
    // Add artificial delay for preview vs full analysis
    if (type === 'full') {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    const result = enhancedComplexityAnalyzer.analyzeCode(code, language);
    
    // Adjust confidence for preview analysis
    if (type === 'preview') {
      result.confidence = Math.max(result.confidence - 15, 30);
      result.explanation = `Preview: ${result.explanation}`;
    }
    
    const response: AnalysisResponse = {
      id,
      result,
      type
    };
    
    self.postMessage(response);
  } catch (error) {
    const response: AnalysisResponse = {
      id,
      result: {
        complexity: 'O(?)',
        explanation: 'Analysis failed. Please check your code syntax.',
        confidence: 0,
        details: {
          loops: 0,
          nestingLevel: 0,
          recursiveCalls: 0,
          patterns: ['Analysis error']
        }
      },
      type,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    
    self.postMessage(response);
  }
};
