<!DOCTYPE html>
<html>
<head>
    <title>Test Sorting Algorithms</title>
</head>
<body>
    <h1>Test Bubble Sort and Selection Sort</h1>
    <pre id="output"></pre>
    
    <script type="module">
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const bubbleSortCode = `void bubbleSort(int arr[], int n) {
    for (int i = 0; i < n - 1; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}`;

        const selectionSortCode = `void selectionSort(int arr[], int n) {
    for (int i = 0; i < n - 1; i++) {
        int min_idx = i;
        for (int j = i + 1; j < n; j++) {
            if (arr[j] < arr[min_idx]) {
                min_idx = j;
            }
        }
        int temp = arr[min_idx];
        arr[min_idx] = arr[i];
        arr[i] = temp;
    }
}`;

        // Test case that should be sequential (for comparison)
        const mergeCode = `void merge(int arr[], int l, int m, int r) {
    int n1 = m - l + 1;
    int n2 = r - m;
    int L[n1], R[n2];

    for (int i = 0; i < n1; i++)
        L[i] = arr[l + i];
    for (int j = 0; j < n2; j++)
        R[j] = arr[m + 1 + j];
}`;

        console.log('=== TESTING SORTING ALGORITHMS ===');
        
        try {
            const parser = new CppParser();
            
            // Test Bubble Sort
            console.log('\n=== BUBBLE SORT ===');
            const bubbleResult = parser.parse(bubbleSortCode);
            console.log('Time Complexity:', bubbleResult.complexity);
            console.log('Expected: O(n^2)');
            console.log('Patterns:', bubbleResult.details.patterns);
            console.log('Max Nesting:', bubbleResult.details.maxNesting);
            console.log('Loop Count:', bubbleResult.details.loopCount);
            
            // Test Selection Sort
            console.log('\n=== SELECTION SORT ===');
            const selectionResult = parser.parse(selectionSortCode);
            console.log('Time Complexity:', selectionResult.complexity);
            console.log('Expected: O(n^2)');
            console.log('Patterns:', selectionResult.details.patterns);
            console.log('Max Nesting:', selectionResult.details.maxNesting);
            console.log('Loop Count:', selectionResult.details.loopCount);

            // Test Merge (Sequential) for comparison
            console.log('\n=== MERGE (SEQUENTIAL) ===');
            const mergeResult = parser.parse(mergeCode);
            console.log('Time Complexity:', mergeResult.complexity);
            console.log('Expected: O(n)');
            console.log('Patterns:', mergeResult.details.patterns);
            console.log('Max Nesting:', mergeResult.details.maxNesting);
            console.log('Loop Count:', mergeResult.details.loopCount);
            
            const output = document.getElementById('output');
            output.textContent = `
Sorting Algorithm Analysis Results:
==================================

BUBBLE SORT:
Time Complexity: ${bubbleResult.complexity}
Expected: O(n^2)
Status: ${bubbleResult.complexity === 'O(n^2)' ? '✅ CORRECT' : '❌ WRONG'}

Loop Count: ${bubbleResult.details.loopCount}
Max Nesting: ${bubbleResult.details.maxNesting}
Confidence: ${bubbleResult.confidence}%

Patterns detected:
${bubbleResult.details.patterns.join('\n')}

---

SELECTION SORT:
Time Complexity: ${selectionResult.complexity}
Expected: O(n^2)
Status: ${selectionResult.complexity === 'O(n^2)' ? '✅ CORRECT' : '❌ WRONG'}

Loop Count: ${selectionResult.details.loopCount}
Max Nesting: ${selectionResult.details.maxNesting}
Confidence: ${selectionResult.confidence}%

Patterns detected:
${selectionResult.details.patterns.join('\n')}

---

MERGE (SEQUENTIAL LOOPS):
Time Complexity: ${mergeResult.complexity}
Expected: O(n)
Status: ${mergeResult.complexity === 'O(n)' ? '✅ CORRECT' : '❌ WRONG'}

Loop Count: ${mergeResult.details.loopCount}
Max Nesting: ${mergeResult.details.maxNesting}
Confidence: ${mergeResult.confidence}%

Patterns detected:
${mergeResult.details.patterns.join('\n')}

---

ANALYSIS:
- Bubble Sort: O(n^2) - Nested loops, outer runs n-1 times, inner runs n-i-1 times
- Selection Sort: O(n^2) - Nested loops, outer runs n-1 times, inner runs n-i times
- Merge: O(n) - Sequential loops, each processes part of the array once
All should be correctly detected by the improved nested loop logic.
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
