import { ComplexityResult } from '../../types/analysis';

export class PythonParser {
  private patterns: string[] = [];
  private loopCount = 0;
  private maxNesting = 0;
  private recursiveCount = 0;
  private functionName: string | null = null;

  parse(code: string): ComplexityResult {
    this.reset();
    
    try {
      return this.analyzeWithIndentation(code);
    } catch (error) {
      console.warn('Python analysis failed:', error);
      return this.getErrorResult();
    }
  }

  private reset() {
    this.patterns = [];
    this.loopCount = 0;
    this.maxNesting = 0;
    this.recursiveCount = 0;
    this.functionName = null;
  }

  private analyzeWithIndentation(code: string): ComplexityResult {
    const lines = code.split('\n').filter(line => line.trim() && !line.trim().startsWith('#'));
    let currentLoopDepth = 0;
    const loopDepthStack: number[] = [];

    // Extract function name
    const funcMatch = code.match(/def\s+(\w+)\s*\(/);
    this.functionName = funcMatch ? funcMatch[1] : null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      const indentLevel = this.getIndentLevel(line);

      // Adjust loop depth based on indentation
      while (loopDepthStack.length > 0 && indentLevel <= loopDepthStack[loopDepthStack.length - 1]) {
        loopDepthStack.pop();
        currentLoopDepth--;
      }

      // Check for loops
      if (this.isLoopLine(trimmed)) {
        this.loopCount++;
        currentLoopDepth++;
        this.maxNesting = Math.max(this.maxNesting, currentLoopDepth);
        loopDepthStack.push(indentLevel);
        this.patterns.push(`Loop at depth ${currentLoopDepth}: ${trimmed}`);
      }

      // Check for recursion
      if (this.functionName && trimmed.includes(this.functionName + '(')) {
        this.recursiveCount++;
        this.patterns.push(`Recursive call: ${trimmed}`);
      }

      // Check for list comprehensions (they're loops too)
      if (this.isListComprehension(trimmed)) {
        this.loopCount++;
        this.patterns.push(`List comprehension: ${trimmed}`);
      }
    }

    return this.calculateComplexity();
  }

  private getIndentLevel(line: string): number {
    const match = line.match(/^(\s*)/);
    return match ? match[1].length : 0;
  }

  private isLoopLine(line: string): boolean {
    return /^(for|while)\s/.test(line);
  }

  private isListComprehension(line: string): boolean {
    return /\[.*for\s+\w+\s+in\s+.*\]/.test(line) || 
           /\{.*for\s+\w+\s+in\s+.*\}/.test(line);
  }

  private calculateComplexity(): ComplexityResult {
    let complexity = 'O(1)';
    let explanation = 'Constant time - no loops or recursive calls detected.';
    let confidence = 95;

    console.log('Python Analysis Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      recursiveCount: this.recursiveCount,
      patterns: this.patterns
    });

    if (this.recursiveCount > 0) {
      if (this.recursiveCount > 1) {
        complexity = 'O(2^n)';
        explanation = 'Exponential time due to multiple recursive calls.';
        confidence = 80;
      } else {
        complexity = 'O(n)';
        explanation = 'Linear time due to recursive processing.';
        confidence = 85;
      }
    } else if (this.maxNesting >= 3) {
      complexity = 'O(n^3)';
      explanation = `Cubic time due to ${this.maxNesting} levels of nested loops.`;
      confidence = 95;
    } else if (this.maxNesting >= 2) {
      complexity = 'O(n^2)';
      explanation = 'Quadratic time due to nested loops.';
      confidence = 95;
    } else if (this.loopCount > 0) {
      complexity = 'O(n)';
      explanation = this.loopCount > 1 ? 
        'Linear time due to sequential loops.' : 
        'Linear time due to a single loop.';
      confidence = 95;
    }

    return {
      complexity,
      explanation,
      confidence: Math.max(confidence, 70),
      details: {
        loops: this.loopCount,
        nestingLevel: this.maxNesting,
        recursiveCalls: this.recursiveCount,
        patterns: this.patterns
      }
    };
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Analysis failed. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }
}