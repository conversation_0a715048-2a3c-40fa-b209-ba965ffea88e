import { ComplexityResult } from '../../types/analysis';

export class PythonParser {
  private patterns: string[] = [];
  private loopCount = 0;
  private maxNesting = 0;
  private recursiveCount = 0;
  private functionName: string | null = null;

  parse(code: string): ComplexityResult {
    this.reset();
    
    try {
      return this.analyzeWithIndentation(code);
    } catch (error) {
      console.warn('Python analysis failed:', error);
      return this.getErrorResult();
    }
  }

  private reset() {
    this.patterns = [];
    this.loopCount = 0;
    this.maxNesting = 0;
    this.recursiveCount = 0;
    this.functionName = null;
  }

  private analyzeWithIndentation(code: string): ComplexityResult {
    const lines = code.split('\n').filter(line => line.trim() && !line.trim().startsWith('#'));
    let currentLoopDepth = 0;
    const loopDepthStack: number[] = [];

    // Extract function name
    const funcMatch = code.match(/def\s+(\w+)\s*\(/);
    this.functionName = funcMatch ? funcMatch[1] : null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      const indentLevel = this.getIndentLevel(line);

      // Adjust loop depth based on indentation
      while (loopDepthStack.length > 0 && indentLevel <= loopDepthStack[loopDepthStack.length - 1]) {
        loopDepthStack.pop();
        currentLoopDepth--;
      }

      // Check for loops
      if (this.isLoopLine(trimmed)) {
        this.loopCount++;
        currentLoopDepth++;
        this.maxNesting = Math.max(this.maxNesting, currentLoopDepth);
        loopDepthStack.push(indentLevel);
        this.patterns.push(`Loop at depth ${currentLoopDepth}: ${trimmed}`);
      }

      // Check for recursion
      if (this.functionName && trimmed.includes(this.functionName + '(')) {
        this.recursiveCount++;
        this.patterns.push(`Recursive call: ${trimmed}`);
      }

      // Check for list comprehensions (they're loops too)
      if (this.isListComprehension(trimmed)) {
        this.loopCount++;
        this.patterns.push(`List comprehension: ${trimmed}`);
      }
    }

    return this.calculateComplexity();
  }

  private getIndentLevel(line: string): number {
    const match = line.match(/^(\s*)/);
    return match ? match[1].length : 0;
  }

  private isLoopLine(line: string): boolean {
    const trimmed = line.trim();

    // For loops
    if (/^for\s+\w+\s+in\s+/.test(trimmed)) return true;

    // While loops
    if (/^while\s+/.test(trimmed)) return true;

    return false;
  }

  private isListComprehension(line: string): boolean {
    // List comprehensions
    if (/\[.*for\s+\w+\s+in\s+.*\]/.test(line)) return true;

    // Set comprehensions
    if (/\{.*for\s+\w+\s+in\s+.*\}/.test(line)) return true;

    // Dict comprehensions
    if (/\{.*:.*for\s+\w+\s+in\s+.*\}/.test(line)) return true;

    // Generator expressions
    if (/\(.*for\s+\w+\s+in\s+.*\)/.test(line)) return true;

    return false;
  }

  private calculateComplexity(): ComplexityResult {
    let complexity = 'O(1)';
    let explanation = 'Constant time - no loops or recursive calls detected.';
    let confidence = 95;

    console.log('Python Analysis Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      recursiveCount: this.recursiveCount,
      patterns: this.patterns,
      functionName: this.functionName
    });

    if (this.recursiveCount > 0) {
      // Improved recursion analysis for Python
      complexity = this.analyzeRecursiveComplexity();
      explanation = this.getRecursiveExplanation(complexity);
      confidence = this.getRecursiveConfidence(complexity);
    } else if (this.maxNesting >= 3) {
      complexity = 'O(n^3)';
      explanation = `Cubic time due to ${this.maxNesting} levels of nested loops.`;
      confidence = 95;
    } else if (this.maxNesting >= 2) {
      complexity = 'O(n^2)';
      explanation = 'Quadratic time due to nested loops.';
      confidence = 95;
    } else if (this.loopCount > 0) {
      complexity = 'O(n)';
      explanation = this.loopCount > 1 ?
        'Linear time due to sequential loops.' :
        'Linear time due to a single loop.';
      confidence = 95;
    }

    return {
      complexity,
      explanation,
      confidence: Math.max(confidence, 70),
      details: {
        loops: this.loopCount,
        nestingLevel: this.maxNesting,
        recursiveCalls: this.recursiveCount,
        patterns: this.patterns
      }
    };
  }

  private analyzeRecursiveComplexity(): string {
    // Check for binary search pattern
    if (this.isBinarySearchPattern()) {
      return 'O(log n)';
    }

    // Check for fibonacci-like pattern
    if (this.recursiveCount >= 2 && this.isFibonacciPattern()) {
      return 'O(2^n)';
    }

    // Single recursive call or tree traversal
    if (this.recursiveCount === 1) {
      return 'O(n)';
    }

    // Multiple recursive calls
    if (this.recursiveCount > 1) {
      return 'O(2^n)';
    }

    return 'O(n)';
  }

  private isBinarySearchPattern(): boolean {
    const patterns = this.patterns.join(' ').toLowerCase();
    return patterns.includes('mid') ||
           patterns.includes('left') && patterns.includes('right') ||
           patterns.includes('//2') || patterns.includes('/ 2');
  }

  private isFibonacciPattern(): boolean {
    const patterns = this.patterns.join(' ').toLowerCase();
    return patterns.includes('n - 1') && patterns.includes('n - 2') ||
           patterns.includes('fibonacci') ||
           (this.recursiveCount === 2 && patterns.includes('return'));
  }

  private getRecursiveExplanation(complexity: string): string {
    switch (complexity) {
      case 'O(log n)':
        return 'Logarithmic time due to divide-and-conquer recursion (like binary search).';
      case 'O(2^n)':
        return 'Exponential time due to multiple recursive calls without memoization.';
      case 'O(n)':
        return 'Linear time due to recursive processing of each element.';
      default:
        return 'Recursive algorithm detected.';
    }
  }

  private getRecursiveConfidence(complexity: string): number {
    switch (complexity) {
      case 'O(log n)':
        return this.isBinarySearchPattern() ? 90 : 75;
      case 'O(2^n)':
        return this.isFibonacciPattern() ? 90 : 80;
      case 'O(n)':
        return 85;
      default:
        return 70;
    }
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Analysis failed. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }
}