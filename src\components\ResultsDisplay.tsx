import React from 'react';
import { Clock, AlertCircle, CheckCircle, TrendingUp, Eye } from 'lucide-react';
import { ComplexityResult } from '../types/analysis';
import { ComplexityCurveChart } from './ComplexityCurveChart';
import { ComplexityComparison } from './ComplexityComparison';

interface ResultsDisplayProps {
  result: ComplexityResult | null;
  isAnalyzing: boolean;
  isPreview?: boolean;
}

const complexityColors = {
  'O(1)': 'text-green-400 bg-green-400/10 border-green-400/20',
  'O(log n)': 'text-green-400 bg-green-400/10 border-green-400/20',
  'O(n)': 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20',
  'O(n log n)': 'text-orange-400 bg-orange-400/10 border-orange-400/20',
  'O(n^2)': 'text-red-400 bg-red-400/10 border-red-400/20',
  'O(n^3)': 'text-red-500 bg-red-500/10 border-red-500/20',
  'O(2^n)': 'text-purple-400 bg-purple-400/10 border-purple-400/20',
  'O(?)': 'text-gray-400 bg-gray-400/10 border-gray-400/20'
};

export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ 
  result, 
  isAnalyzing, 
  isPreview = false 
}) => {
  if (isAnalyzing) {
    return (
      <div className="space-y-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-300">Performing deep analysis...</span>
          </div>
          <div className="mt-4 bg-gray-900/50 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span>AST parsing and pattern recognition in progress</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="space-y-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="text-center text-gray-400">
            <Clock className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>Start typing code above to see real-time complexity analysis.</p>
            <div className="mt-4 text-xs text-gray-500">
              • Preview analysis appears as you type<br/>
              • Full analysis after you pause typing<br/>
              • History tracking for all analyses
            </div>
          </div>
        </div>
      </div>
    );
  }

  const complexityStyle = complexityColors[result.complexity as keyof typeof complexityColors] || complexityColors['O(?)'];

  return (
    <div className="space-y-6">
      {/* Main Result */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        {/* Preview/Full Analysis Indicator */}
        {isPreview && (
          <div className="mb-4 flex items-center justify-center">
            <div className="inline-flex items-center space-x-2 px-3 py-1 bg-yellow-400/10 border border-yellow-400/20 rounded-full text-yellow-400">
              <Eye className="w-4 h-4" />
              <span className="text-sm font-medium">Live Preview</span>
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        )}

        <div className="text-center mb-6">
          <div className={`inline-flex items-center px-4 py-2 rounded-full border ${complexityStyle} font-mono text-lg font-bold`}>
            <TrendingUp className="w-5 h-5 mr-2" />
            {result.complexity}
          </div>
          
          {isPreview && (
            <div className="mt-2 text-xs text-yellow-400">
              Preliminary analysis • Full results coming soon
            </div>
          )}
        </div>

        {/* Explanation */}
        <div className="bg-gray-900/50 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
            <AlertCircle className="w-5 h-5 mr-2 text-blue-400" />
            {isPreview ? 'Quick Analysis' : 'Detailed Explanation'}
          </h3>
          <p className="text-gray-300 leading-relaxed">
            {result.explanation}
          </p>
          
          {isPreview && (
            <div className="mt-3 p-2 bg-yellow-400/5 border border-yellow-400/20 rounded text-xs text-yellow-300">
              💡 This is a quick preview. Keep typing for more accurate analysis with higher confidence.
            </div>
          )}
        </div>

        {/* Details */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-900/50 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-400">{result.details.loops}</div>
            <div className="text-xs text-gray-400 uppercase tracking-wide">Loops</div>
          </div>
          <div className="bg-gray-900/50 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-400">{result.details.nestingLevel}</div>
            <div className="text-xs text-gray-400 uppercase tracking-wide">Max Nesting</div>
          </div>
          <div className="bg-gray-900/50 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-orange-400">{result.details.recursiveCalls}</div>
            <div className="text-xs text-gray-400 uppercase tracking-wide">Recursive Calls</div>
          </div>
          <div className="bg-gray-900/50 rounded-lg p-3 text-center">
            <div className={`text-2xl font-bold ${isPreview ? 'text-yellow-400' : 'text-green-400'}`}>
              {result.confidence}%
            </div>
            <div className="text-xs text-gray-400 uppercase tracking-wide">
              {isPreview ? 'Preview Conf.' : 'Confidence'}
            </div>
          </div>
        </div>

        {/* Space Complexity Section */}
        {result.space && (
          <div className="mt-6 bg-gray-900/50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-cyan-400" />
              Space Complexity Analysis
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Space Complexity Result */}
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400 text-sm">Space Complexity</span>
                  <span className="text-cyan-400 font-mono font-bold text-lg">{result.space.spaceComplexity}</span>
                </div>
                <p className="text-gray-300 text-sm">{result.space.explanation}</p>
                <div className="mt-2 flex items-center">
                  <span className="text-xs text-gray-400">Confidence: </span>
                  <span className="text-xs text-cyan-400 ml-1">{result.space.confidence}%</span>
                </div>
              </div>

              {/* Space Details */}
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="text-center">
                    <div className="text-lg font-bold text-cyan-400">{result.space.details.variables}</div>
                    <div className="text-xs text-gray-400">Variables</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-cyan-400">{result.space.details.recursionDepth}</div>
                    <div className="text-xs text-gray-400">Recursion Depth</div>
                  </div>
                  <div className="col-span-2 text-center">
                    <div className="text-lg font-bold text-cyan-400">{result.space.details.dataStructures.length}</div>
                    <div className="text-xs text-gray-400">Data Structures</div>
                  </div>
                </div>

                {result.space.details.dataStructures.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-700">
                    <div className="text-xs text-gray-400 mb-1">Detected:</div>
                    <div className="flex flex-wrap gap-1">
                      {result.space.details.dataStructures.map((ds, index) => (
                        <span key={index} className="px-2 py-1 bg-cyan-500/20 text-cyan-300 rounded text-xs">
                          {ds}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Patterns */}
        {result.details.patterns.length > 0 && (
          <div className="bg-gray-900/50 rounded-lg p-4 mt-6">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
              Detected Patterns
              {isPreview && (
                <span className="ml-2 px-2 py-1 bg-yellow-400/20 rounded text-xs text-yellow-300">
                  Preview
                </span>
              )}
            </h3>
            <div className="space-y-2">
              {result.details.patterns.slice(0, isPreview ? 3 : 5).map((pattern, index) => (
                <div key={index} className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 flex-shrink-0"></div>
                  <code className="text-gray-300 bg-gray-800 px-2 py-1 rounded text-xs">{pattern}</code>
                </div>
              ))}
              {result.details.patterns.length > (isPreview ? 3 : 5) && (
                <div className="text-xs text-gray-400 ml-5">
                  ... and {result.details.patterns.length - (isPreview ? 3 : 5)} more patterns
                  {isPreview && ' (shown in full analysis)'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Only show detailed visualizations for full analysis */}
      {!isPreview && (
        <>
          {/* Visual Complexity Curve */}
          <ComplexityCurveChart complexity={result.complexity} />

          {/* Complexity Comparison */}
          <ComplexityComparison currentComplexity={result.complexity} />
        </>
      )}
    </div>
  );
};