export interface AlgorithmPattern {
  name: string;
  timeComplexity: string;
  spaceComplexity: string;
  confidence: number;
  characteristics: string[];
  optimizations: string[];
}

export class AlgorithmDetector {
  detectSortingAlgorithms(code: string, patterns: string[]): AlgorithmPattern | null {
    const codeText = code.toLowerCase();
    const patternText = patterns.join(' ').toLowerCase();

    // Bubble Sort Detection
    if (this.isBubbleSort(codeText, patternText)) {
      return {
        name: 'Bubble Sort',
        timeComplexity: 'O(n²)',
        spaceComplexity: 'O(1)',
        confidence: 90,
        characteristics: ['Nested loops', 'Adjacent element swapping', 'In-place sorting'],
        optimizations: ['Use Quick Sort O(n log n)', 'Use Merge Sort for stability', 'Add early termination']
      };
    }

    // Quick Sort Detection
    if (this.isQuickSort(codeText, patternText)) {
      return {
        name: 'Quick Sort',
        timeComplexity: 'O(n log n)',
        spaceComplexity: 'O(log n)',
        confidence: 85,
        characteristics: ['Divide and conquer', 'Pivot selection', 'Recursive partitioning'],
        optimizations: ['Use 3-way partitioning', 'Hybrid with insertion sort', 'Randomized pivot']
      };
    }

    // Merge Sort Detection
    if (this.isMergeSort(codeText, patternText)) {
      return {
        name: 'Merge Sort',
        timeComplexity: 'O(n log n)',
        spaceComplexity: 'O(n)',
        confidence: 85,
        characteristics: ['Divide and conquer', 'Stable sorting', 'Merge operation'],
        optimizations: ['In-place merge', 'Bottom-up approach', 'Natural merge sort']
      };
    }

    return null;
  }

  detectSearchAlgorithms(code: string, patterns: string[]): AlgorithmPattern | null {
    const codeText = code.toLowerCase();
    const patternText = patterns.join(' ').toLowerCase();

    // Binary Search Detection
    if (this.isBinarySearch(codeText, patternText)) {
      return {
        name: 'Binary Search',
        timeComplexity: 'O(log n)',
        spaceComplexity: 'O(1)',
        confidence: 90,
        characteristics: ['Divide and conquer', 'Sorted array requirement', 'Logarithmic reduction'],
        optimizations: ['Iterative version', 'Interpolation search', 'Exponential search']
      };
    }

    // Linear Search Detection
    if (this.isLinearSearch(codeText, patternText)) {
      return {
        name: 'Linear Search',
        timeComplexity: 'O(n)',
        spaceComplexity: 'O(1)',
        confidence: 95,
        characteristics: ['Sequential scanning', 'No sorting requirement', 'Simple implementation'],
        optimizations: ['Use binary search if sorted', 'Use hash table for O(1)', 'Early termination']
      };
    }

    return null;
  }

  detectGraphAlgorithms(code: string, patterns: string[]): AlgorithmPattern | null {
    const codeText = code.toLowerCase();
    const patternText = patterns.join(' ').toLowerCase();

    // DFS Detection
    if (this.isDFS(codeText, patternText)) {
      return {
        name: 'Depth-First Search (DFS)',
        timeComplexity: 'O(V + E)',
        spaceComplexity: 'O(V)',
        confidence: 80,
        characteristics: ['Stack-based traversal', 'Recursive implementation', 'Backtracking'],
        optimizations: ['Iterative with explicit stack', 'Path compression', 'Memoization']
      };
    }

    // BFS Detection
    if (this.isBFS(codeText, patternText)) {
      return {
        name: 'Breadth-First Search (BFS)',
        timeComplexity: 'O(V + E)',
        spaceComplexity: 'O(V)',
        confidence: 80,
        characteristics: ['Queue-based traversal', 'Level-order processing', 'Shortest path'],
        optimizations: ['Bidirectional BFS', 'A* algorithm', 'Dijkstra for weighted graphs']
      };
    }

    return null;
  }

  private isBubbleSort(code: string, patterns: string): boolean {
    return patterns.includes('nested') && 
           (code.includes('swap') || code.includes('temp')) &&
           patterns.includes('loop at nesting level 2');
  }

  private isQuickSort(code: string, patterns: string): boolean {
    return code.includes('partition') || 
           (code.includes('pivot') && patterns.includes('recursive')) ||
           (patterns.includes('recursive') && code.includes('quicksort'));
  }

  private isMergeSort(code: string, patterns: string): boolean {
    return code.includes('merge') && patterns.includes('recursive') ||
           (code.includes('mergesort') || code.includes('merge_sort'));
  }

  private isBinarySearch(code: string, patterns: string): boolean {
    return patterns.includes('mid') || 
           (patterns.includes('recursive') && code.includes('binary')) ||
           (code.includes('left') && code.includes('right') && code.includes('mid'));
  }

  private isLinearSearch(code: string, patterns: string): boolean {
    return patterns.includes('loop at nesting level 1') && 
           !patterns.includes('nested') &&
           (code.includes('search') || code.includes('find'));
  }

  private isDFS(code: string, patterns: string): boolean {
    return (code.includes('dfs') || code.includes('depth')) &&
           (patterns.includes('recursive') || code.includes('stack'));
  }

  private isBFS(code: string, patterns: string): boolean {
    return (code.includes('bfs') || code.includes('breadth')) &&
           code.includes('queue');
  }
}
