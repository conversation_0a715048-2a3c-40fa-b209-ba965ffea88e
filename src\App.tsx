import React, { useState } from 'react';
import { Calculator, Github, BookOpen, Zap } from 'lucide-react';
import { CodeEditor } from './components/CodeEditor';
import { LanguageSelector } from './components/LanguageSelector';
import { ResultsDisplay } from './components/ResultsDisplay';
import { ExampleSelector } from './components/ExampleSelector';
import { RealTimeIndicator } from './components/RealTimeIndicator';
import { AnalysisHistory } from './components/AnalysisHistory';
import { PreviewBadge } from './components/PreviewBadge';
import { useRealTimeAnalysis } from './hooks/useRealTimeAnalysis';
import { SupportedLanguage } from './types/analysis';

function App() {
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState<SupportedLanguage>('cpp');

  const {
    result,
    previewResult,
    isAnalyzing,
    analysisHistory,
    triggerAnalysis,
    clearAnalysis,
    hasPreview,
    isRealTimeEnabled
  } = useRealTimeAnalysis(code, language, {
    debounceMs: 800,
    minCodeLength: 10,
    enablePreview: true
  });

  const handleExampleSelect = (exampleCode: string, exampleLanguage: SupportedLanguage) => {
    setCode(exampleCode);
    setLanguage(exampleLanguage);
    clearAnalysis();
  };

  const handleCodeChange = (newCode: string) => {
    setCode(newCode);
  };

  const isCodeEmpty = !code.trim();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Header */}
      <header className="border-b border-gray-700 bg-gray-900/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg">
                <Calculator className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Big-O Analyzer</h1>
                <p className="text-sm text-gray-400">Real-time AST-based Time Complexity Analysis</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-2 px-3 py-1 bg-green-500/10 border border-green-500/20 rounded-full">
                <Zap className="w-4 h-4 text-green-400" />
                <span className="text-sm text-green-400 font-medium">Real-time Analysis</span>
              </div>
              <a
                href="https://github.com"
                className="text-gray-400 hover:text-white transition-colors"
                title="View on GitHub"
              >
                <Github className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
                title="Documentation"
              >
                <BookOpen className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Code Input */}
          <div className="space-y-6">
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Code Input</h2>
                <div className="flex items-center space-x-3">
                  <LanguageSelector value={language} onChange={setLanguage} />
                </div>
              </div>
              
              {/* Real-time Status */}
              <div className="mb-4 flex items-center justify-between">
                <RealTimeIndicator
                  isAnalyzing={isAnalyzing}
                  hasPreview={hasPreview}
                  isRealTimeEnabled={isRealTimeEnabled}
                  confidence={result?.confidence || previewResult?.confidence}
                />
                
                {previewResult && (
                  <PreviewBadge previewResult={previewResult} />
                )}
              </div>
              
              <CodeEditor
                value={code}
                onChange={handleCodeChange}
                language={language}
              />
              
              <div className="mt-4 flex justify-between items-center">
                <div className="text-xs text-gray-400">
                  {code.length > 0 && (
                    <span>{code.length} characters • Real-time analysis enabled</span>
                  )}
                </div>
                <div className="flex space-x-2">
                  {code.length > 0 && (
                    <button
                      onClick={clearAnalysis}
                      className="px-4 py-2 text-gray-400 hover:text-white border border-gray-600 hover:border-gray-500 rounded-lg transition-colors text-sm"
                    >
                      Clear
                    </button>
                  )}
                  <button
                    onClick={triggerAnalysis}
                    disabled={isCodeEmpty || isAnalyzing}
                    className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100 shadow-lg"
                  >
                    {isAnalyzing ? 'Analyzing...' : 'Analyze Now'}
                  </button>
                </div>
              </div>
            </div>

            <ExampleSelector onSelectExample={handleExampleSelect} />
          </div>

          {/* Right Column - Results */}
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-semibold text-white mb-4">Analysis Results</h2>
              <ResultsDisplay 
                result={result || previewResult} 
                isAnalyzing={isAnalyzing}
                isPreview={hasPreview && !result}
              />
            </div>

            {/* Analysis History */}
            <AnalysisHistory 
              history={analysisHistory}
              onSelectResult={(selectedResult) => {
                // Could implement result comparison or detailed view
                console.log('Selected result:', selectedResult);
              }}
            />

            {/* Enhanced Info Panel */}
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-3">Advanced Analysis Features</h3>
              <div className="space-y-3 text-sm text-gray-300">
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p><strong>Instant Preview:</strong> See complexity estimates as you type with 150ms response time</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p><strong>Smart Debouncing:</strong> Full analysis triggered after 800ms of inactivity</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p><strong>Space Complexity:</strong> Analyze memory usage alongside time complexity</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p><strong>Analysis History:</strong> Track complexity changes as you modify your code</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p><strong>Confidence Scoring:</strong> Real-time reliability metrics for each analysis</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p><strong>Progressive Enhancement:</strong> Preview → Full Analysis → History tracking</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t border-gray-700 bg-gray-900/50 backdrop-blur-sm mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-gray-400 text-sm">
            <p>Enhanced with real-time AST parsing for instant algorithm complexity feedback.</p>
            <p className="mt-1">© 2025 Big-O Analyzer. Built with modern parsing techniques and real-time analysis.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;