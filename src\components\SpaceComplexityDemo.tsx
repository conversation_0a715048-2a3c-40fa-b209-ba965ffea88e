import React, { useState } from 'react';
import { MemoryStick, Zap, TrendingUp, Database } from 'lucide-react';
import { enhancedComplexityAnalyzer } from '../utils/enhancedComplexityAnalyzer';
import { SupportedLanguage } from '../types/analysis';

interface SpaceComplexityExample {
  name: string;
  language: SupportedLanguage;
  code: string;
  expectedSpace: string;
  description: string;
}

const spaceExamples: SpaceComplexityExample[] = [
  {
    name: 'Constant Space - Linear Search',
    language: 'cpp',
    code: `int linearSearch(int arr[], int n, int target) {
    for (int i = 0; i < n; i++) {
        if (arr[i] == target) {
            return i;
        }
    }
    return -1;
}`,
    expectedSpace: 'O(1)',
    description: 'Uses only a fixed amount of extra memory regardless of input size.'
  },
  {
    name: 'Linear Space - Recursive Factorial',
    language: 'cpp',
    code: `int factorial(int n) {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}`,
    expectedSpace: 'O(n)',
    description: 'Recursion creates a call stack proportional to input size.'
  },
  {
    name: 'Linear Space - Merge Sort',
    language: 'cpp',
    code: `void merge(int arr[], int l, int m, int r) {
    int n1 = m - l + 1;
    int n2 = r - m;
    int L[n1], R[n2];  // Auxiliary arrays
    
    for (int i = 0; i < n1; i++)
        L[i] = arr[l + i];
    for (int j = 0; j < n2; j++)
        R[j] = arr[m + 1 + j];
}`,
    expectedSpace: 'O(n)',
    description: 'Creates auxiliary arrays for merging, requiring linear extra space.'
  },
  {
    name: 'Linear Space - Python List Comprehension',
    language: 'python',
    code: `def process_numbers(numbers):
    doubled = [x * 2 for x in numbers]
    filtered = [x for x in doubled if x > 10]
    return filtered`,
    expectedSpace: 'O(n)',
    description: 'List comprehensions create new lists, using space proportional to input.'
  },
  {
    name: 'Linear Space - JavaScript Array Methods',
    language: 'javascript',
    code: `function processData(data) {
    const doubled = data.map(x => x * 2);
    const filtered = doubled.filter(x => x > 10);
    return filtered.reduce((sum, x) => sum + x, 0);
}`,
    expectedSpace: 'O(n)',
    description: 'Array methods like map() and filter() create new arrays.'
  }
];

export const SpaceComplexityDemo: React.FC = () => {
  const [selectedExample, setSelectedExample] = useState<SpaceComplexityExample>(spaceExamples[0]);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const analyzeExample = async (example: SpaceComplexityExample) => {
    setIsAnalyzing(true);
    setSelectedExample(example);
    
    // Simulate analysis delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const result = enhancedComplexityAnalyzer.analyzeCode(example.code, example.language);
    setAnalysisResult(result);
    setIsAnalyzing(false);
  };

  React.useEffect(() => {
    analyzeExample(spaceExamples[0]);
  }, []);

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
        <MemoryStick className="w-6 h-6 mr-2 text-cyan-400" />
        Space Complexity Analysis Demo
      </h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Example Selection */}
        <div>
          <h4 className="text-lg font-medium text-white mb-3">Select an Example</h4>
          <div className="space-y-2">
            {spaceExamples.map((example, index) => (
              <button
                key={index}
                onClick={() => analyzeExample(example)}
                className={`w-full text-left p-3 rounded-lg border transition-all ${
                  selectedExample.name === example.name
                    ? 'border-cyan-500 bg-cyan-500/10'
                    : 'border-gray-600 bg-gray-900/50 hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium">{example.name}</span>
                  <span className="text-cyan-400 font-mono text-sm">{example.expectedSpace}</span>
                </div>
                <p className="text-gray-400 text-sm mt-1">{example.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Analysis Results */}
        <div>
          <h4 className="text-lg font-medium text-white mb-3">Analysis Results</h4>
          
          {isAnalyzing ? (
            <div className="bg-gray-900/50 rounded-lg p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500 mx-auto mb-3"></div>
              <p className="text-gray-300">Analyzing space complexity...</p>
            </div>
          ) : analysisResult ? (
            <div className="space-y-4">
              {/* Time vs Space Comparison */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-900/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">{analysisResult.complexity}</div>
                  <div className="text-xs text-gray-400 uppercase tracking-wide">Time Complexity</div>
                </div>
                <div className="bg-gray-900/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-cyan-400">
                    {analysisResult.space?.spaceComplexity || 'N/A'}
                  </div>
                  <div className="text-xs text-gray-400 uppercase tracking-wide">Space Complexity</div>
                </div>
              </div>

              {/* Space Analysis Details */}
              {analysisResult.space && (
                <div className="bg-gray-900/50 rounded-lg p-4">
                  <h5 className="text-white font-medium mb-2 flex items-center">
                    <Database className="w-4 h-4 mr-2 text-cyan-400" />
                    Space Analysis Details
                  </h5>
                  
                  <div className="grid grid-cols-3 gap-3 mb-3">
                    <div className="text-center">
                      <div className="text-lg font-bold text-cyan-400">{analysisResult.space.details.variables}</div>
                      <div className="text-xs text-gray-400">Variables</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-cyan-400">{analysisResult.space.details.recursionDepth}</div>
                      <div className="text-xs text-gray-400">Recursion Depth</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-cyan-400">{analysisResult.space.details.dataStructures.length}</div>
                      <div className="text-xs text-gray-400">Data Structures</div>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm mb-2">{analysisResult.space.explanation}</p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">Confidence:</span>
                    <span className="text-xs text-cyan-400">{analysisResult.space.confidence}%</span>
                  </div>

                  {analysisResult.space.details.dataStructures.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-700">
                      <div className="text-xs text-gray-400 mb-1">Data Structures Detected:</div>
                      <div className="flex flex-wrap gap-1">
                        {analysisResult.space.details.dataStructures.map((ds: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-cyan-500/20 text-cyan-300 rounded text-xs">
                            {ds}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Code Preview */}
              <div className="bg-gray-900/50 rounded-lg p-4">
                <h5 className="text-white font-medium mb-2">Code</h5>
                <pre className="text-sm text-gray-300 overflow-x-auto">
                  <code>{selectedExample.code}</code>
                </pre>
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Educational Note */}
      <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h5 className="text-blue-400 font-medium mb-2 flex items-center">
          <Zap className="w-4 h-4 mr-2" />
          Understanding Space Complexity
        </h5>
        <p className="text-blue-300 text-sm">
          Space complexity measures the amount of extra memory an algorithm uses relative to the input size. 
          It includes auxiliary data structures, recursion stack depth, and temporary variables. 
          Understanding both time and space complexity helps you make informed decisions about algorithm trade-offs.
        </p>
      </div>
    </div>
  );
};
