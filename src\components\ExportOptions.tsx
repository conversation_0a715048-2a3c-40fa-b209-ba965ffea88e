import React, { useState } from 'react';
import { Download, Share2, <PERSON><PERSON>, FileText, Image } from 'lucide-react';
import { ComplexityResult } from '../types/analysis';

interface ExportOptionsProps {
  result: ComplexityResult;
  code: string;
  language: string;
}

export const ExportOptions: React.FC<ExportOptionsProps> = ({ result, code, language }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  const generateReport = () => {
    const timestamp = new Date().toISOString();
    return {
      analysis: {
        timestamp,
        language,
        complexity: result.complexity,
        confidence: result.confidence,
        explanation: result.explanation,
        details: result.details
      },
      code: code,
      metadata: {
        tool: 'Big-O Analyzer',
        version: '1.0.0'
      }
    };
  };

  const exportAsJSON = () => {
    const report = generateReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `complexity-analysis-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportAsMarkdown = () => {
    const report = generateReport();
    const markdown = `# Complexity Analysis Report

**Generated**: ${new Date(report.analysis.timestamp).toLocaleString()}
**Language**: ${report.analysis.language}

## Analysis Results

- **Time Complexity**: ${report.analysis.complexity}
- **Confidence**: ${report.analysis.confidence}%
- **Loops**: ${report.analysis.details.loops}
- **Max Nesting**: ${report.analysis.details.nestingLevel}
- **Recursive Calls**: ${report.analysis.details.recursiveCalls}

## Explanation

${report.analysis.explanation}

## Detected Patterns

${report.analysis.details.patterns.map(p => `- ${p}`).join('\n')}

## Code

\`\`\`${report.analysis.language}
${report.code}
\`\`\`

---
*Generated by Big-O Analyzer v${report.metadata.version}*
`;

    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `complexity-analysis-${Date.now()}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyToClipboard = async () => {
    const report = generateReport();
    const text = `Complexity Analysis: ${report.analysis.complexity} (${report.analysis.confidence}% confidence)\n${report.analysis.explanation}`;
    
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const shareAnalysis = async () => {
    const report = generateReport();
    const shareData = {
      title: 'Code Complexity Analysis',
      text: `My code has ${report.analysis.complexity} time complexity with ${report.analysis.confidence}% confidence. ${report.analysis.explanation}`,
      url: window.location.href
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      await copyToClipboard();
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
      >
        <Share2 className="w-4 h-4" />
        <span>Export</span>
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10">
          <div className="p-2 space-y-1">
            <button
              onClick={copyToClipboard}
              className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-300 hover:bg-gray-700 rounded transition-colors"
            >
              <Copy className="w-4 h-4" />
              <span>{copied ? 'Copied!' : 'Copy Summary'}</span>
            </button>

            <button
              onClick={shareAnalysis}
              className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-300 hover:bg-gray-700 rounded transition-colors"
            >
              <Share2 className="w-4 h-4" />
              <span>Share Analysis</span>
            </button>

            <button
              onClick={exportAsJSON}
              className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-300 hover:bg-gray-700 rounded transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Export JSON</span>
            </button>

            <button
              onClick={exportAsMarkdown}
              className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-300 hover:bg-gray-700 rounded transition-colors"
            >
              <FileText className="w-4 h-4" />
              <span>Export Markdown</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
