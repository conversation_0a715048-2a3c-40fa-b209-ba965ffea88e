<!DOCTYPE html>
<html>
<head>
    <title>Test Fibonacci Fix</title>
</head>
<body>
    <h1>Test Fibonacci Complexity Fix</h1>
    <pre id="output"></pre>
    
    <script type="module">
        // Import the parser directly
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const fibonacciCode = `int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}`;

        console.log('=== TESTING FIBONACCI FIX ===');
        console.log('Code to analyze:');
        console.log(fibonacciCode);
        
        try {
            const parser = new CppParser();
            const result = parser.parse(fibonacciCode);
            
            console.log('\n=== ANALYSIS RESULT ===');
            console.log('Time Complexity:', result.complexity);
            console.log('Expected: O(2^n)');
            console.log('Recursive calls found:', result.details.recursiveCalls);
            console.log('Expected: 2');
            console.log('Patterns:', result.details.patterns);
            console.log('Space Complexity:', result.space?.spaceComplexity);
            console.log('Space Expected: O(n)');
            
            const output = document.getElementById('output');
            output.textContent = `
Fibonacci Analysis Results:
==========================
Time Complexity: ${result.complexity}
Expected: O(2^n)
Status: ${result.complexity === 'O(2^n)' ? '✅ FIXED!' : '❌ Still broken'}

Recursive Calls: ${result.details.recursiveCalls}
Expected: 2
Status: ${result.details.recursiveCalls === 2 ? '✅ CORRECT' : '❌ Incorrect count'}

Space Complexity: ${result.space?.spaceComplexity || 'N/A'}
Expected: O(n)
Status: ${result.space?.spaceComplexity === 'O(n)' ? '✅ CORRECT' : '❌ Incorrect'}

Confidence: ${result.confidence}%
Explanation: ${result.explanation}

Patterns detected:
${result.details.patterns.join('\n')}

Space Details:
Variables: ${result.space?.details.variables || 0}
Recursion Depth: ${result.space?.details.recursionDepth || 0}
Data Structures: ${result.space?.details.dataStructures.join(', ') || 'None'}
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
