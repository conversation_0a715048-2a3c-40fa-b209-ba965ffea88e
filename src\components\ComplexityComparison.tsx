import React from 'react';
import { BarChart3, Zap, AlertTriangle } from 'lucide-react';

interface ComplexityComparisonProps {
  currentComplexity: string;
  className?: string;
}

const complexityRankings = [
  { complexity: 'O(1)', label: 'Constant', color: '#10b981', rank: 1, performance: 'Excellent' },
  { complexity: 'O(log n)', label: 'Logarithmic', color: '#22c55e', rank: 2, performance: 'Excellent' },
  { complexity: 'O(n)', label: 'Linear', color: '#eab308', rank: 3, performance: 'Good' },
  { complexity: 'O(n log n)', label: 'Linearithmic', color: '#f97316', rank: 4, performance: 'Good' },
  { complexity: 'O(n^2)', label: 'Quadratic', color: '#ef4444', rank: 5, performance: 'Fair' },
  { complexity: 'O(n^3)', label: 'Cubic', color: '#dc2626', rank: 6, performance: 'Poor' },
  { complexity: 'O(2^n)', label: 'Exponential', color: '#9333ea', rank: 7, performance: 'Very Poor' }
];

const getPerformanceIcon = (performance: string) => {
  switch (performance) {
    case 'Excellent':
      return <Zap className="w-4 h-4 text-green-400" />;
    case 'Good':
      return <Zap className="w-4 h-4 text-yellow-400" />;
    case 'Fair':
      return <AlertTriangle className="w-4 h-4 text-orange-400" />;
    case 'Poor':
    case 'Very Poor':
      return <AlertTriangle className="w-4 h-4 text-red-400" />;
    default:
      return <BarChart3 className="w-4 h-4 text-gray-400" />;
  }
};

export const ComplexityComparison: React.FC<ComplexityComparisonProps> = ({ 
  currentComplexity, 
  className = '' 
}) => {
  const currentRank = complexityRankings.find(c => c.complexity === currentComplexity)?.rank || 0;

  return (
    <div className={`bg-gray-800 rounded-lg p-6 border border-gray-700 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <BarChart3 className="w-5 h-5 mr-2 text-blue-400" />
        Complexity Comparison
      </h3>
      
      <div className="space-y-3">
        {complexityRankings.map((item, index) => {
          const isCurrent = item.complexity === currentComplexity;
          const isBetter = item.rank < currentRank;
          const isWorse = item.rank > currentRank;
          
          return (
            <div
              key={item.complexity}
              className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
                isCurrent 
                  ? 'bg-blue-500/10 border-blue-500/30 ring-1 ring-blue-500/20' 
                  : isBetter
                  ? 'bg-green-500/5 border-green-500/20 hover:bg-green-500/10'
                  : isWorse
                  ? 'bg-red-500/5 border-red-500/20'
                  : 'bg-gray-900/50 border-gray-700 hover:bg-gray-900'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className={`font-mono text-sm font-bold ${
                    isCurrent ? 'text-blue-400' : 'text-gray-300'
                  }`}>
                    {item.complexity}
                  </span>
                </div>
                <span className={`text-sm ${
                  isCurrent ? 'text-blue-300' : 'text-gray-400'
                }`}>
                  {item.label}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                {getPerformanceIcon(item.performance)}
                <span className={`text-xs font-medium ${
                  isCurrent ? 'text-blue-300' : 'text-gray-400'
                }`}>
                  {item.performance}
                </span>
                {isCurrent && (
                  <div className="px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300 font-medium">
                    Current
                  </div>
                )}
                {isBetter && currentRank > 0 && (
                  <div className="px-2 py-1 bg-green-500/20 rounded text-xs text-green-300 font-medium">
                    Better
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      {currentRank > 3 && (
        <div className="mt-4 p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-orange-300">
              <strong>Optimization Opportunity:</strong> Consider algorithms with better time complexity 
              for improved performance on large datasets.
            </div>
          </div>
        </div>
      )}
      
      {currentRank <= 2 && (
        <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
          <div className="flex items-start space-x-2">
            <Zap className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-green-300">
              <strong>Excellent Performance:</strong> This algorithm has optimal or near-optimal 
              time complexity for most use cases.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};