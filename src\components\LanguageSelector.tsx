import React from 'react';
import { ChevronDown } from 'lucide-react';
import { SupportedLanguage } from '../types/analysis';

interface LanguageSelectorProps {
  value: SupportedLanguage;
  onChange: (language: SupportedLanguage) => void;
}

const languages = [
  { value: 'python' as const, label: 'Python', icon: '🐍' },
  { value: 'cpp' as const, label: 'C++', icon: '⚡' },
  { value: 'javascript' as const, label: 'JavaScript', icon: '🌐' }
];

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({ value, onChange }) => {
  return (
    <div className="relative">
      <select
        value={value}
        onChange={(e) => onChange(e.target.value as SupportedLanguage)}
        className="appearance-none bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 pr-10 text-white focus:outline-none focus:border-blue-500 transition-colors cursor-pointer hover:border-gray-600"
      >
        {languages.map((lang) => (
          <option key={lang.value} value={lang.value}>
            {lang.icon} {lang.label}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>
  );
};