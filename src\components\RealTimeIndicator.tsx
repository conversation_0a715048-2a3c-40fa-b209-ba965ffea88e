import React from 'react';
import { Zap, Clock, CheckCircle, AlertCircle } from 'lucide-react';

interface RealTimeIndicatorProps {
  isAnalyzing: boolean;
  hasPreview: boolean;
  isRealTimeEnabled: boolean;
  confidence?: number;
  className?: string;
}

export const RealTimeIndicator: React.FC<RealTimeIndicatorProps> = ({
  isAnalyzing,
  hasPreview,
  isRealTimeEnabled,
  confidence = 0,
  className = ''
}) => {
  if (!isRealTimeEnabled) {
    return (
      <div className={`flex items-center space-x-2 text-gray-500 ${className}`}>
        <Clock className="w-4 h-4" />
        <span className="text-sm">Type code to start analysis...</span>
      </div>
    );
  }

  if (isAnalyzing) {
    return (
      <div className={`flex items-center space-x-2 text-blue-400 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
        <span className="text-sm">Analyzing...</span>
      </div>
    );
  }

  if (hasPreview) {
    return (
      <div className={`flex items-center space-x-2 text-yellow-400 ${className}`}>
        <Zap className="w-4 h-4" />
        <span className="text-sm">Preview analysis</span>
        <div className="px-2 py-1 bg-yellow-400/10 rounded text-xs">
          Live
        </div>
      </div>
    );
  }

  const getConfidenceColor = (conf: number) => {
    if (conf >= 90) return 'text-green-400';
    if (conf >= 70) return 'text-yellow-400';
    if (conf >= 50) return 'text-orange-400';
    return 'text-red-400';
  };

  const getConfidenceIcon = (conf: number) => {
    if (conf >= 70) return <CheckCircle className="w-4 h-4" />;
    return <AlertCircle className="w-4 h-4" />;
  };

  return (
    <div className={`flex items-center space-x-2 ${getConfidenceColor(confidence)} ${className}`}>
      {getConfidenceIcon(confidence)}
      <span className="text-sm">Analysis complete</span>
      <div className="px-2 py-1 bg-current/10 rounded text-xs">
        {confidence}% confidence
      </div>
    </div>
  );
};