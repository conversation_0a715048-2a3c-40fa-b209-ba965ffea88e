import { ComplexityResult, SupportedLanguage, EnhancedComplexityResult } from '../types/analysis';
import { PythonParser } from './parsers/pythonParser';
import { CppParser } from './parsers/cppParser';
import { JavaScriptParser } from './parsers/javascriptParser';
import { SpaceComplexityAnalyzer } from './spaceComplexityAnalyzer';

class EnhancedComplexityAnalyzer {
  private pythonParser = new PythonParser();
  private cppParser = new CppParser();
  private jsParser = new JavaScriptParser();
  private spaceAnalyzer = new SpaceComplexityAnalyzer();

  analyzeCode(code: string, language: SupportedLanguage): ComplexityResult {
    if (!code.trim()) {
      return this.getEmptyResult();
    }

    try {
      const timeResult = this.analyzeTimeComplexity(code, language);
      const spaceResult = this.spaceAnalyzer.analyzeSpace(code, language, timeResult.details.recursiveCalls);

      // Combine time and space analysis
      return {
        ...timeResult,
        space: spaceResult
      };
    } catch (error) {
      console.error('Analysis failed:', error);
      return this.getErrorResult();
    }
  }

  analyzeEnhanced(code: string, language: SupportedLanguage): EnhancedComplexityResult {
    const timeResult = this.analyzeTimeComplexity(code, language);
    const spaceResult = this.spaceAnalyzer.analyzeSpace(code, language, timeResult.details.recursiveCalls);

    return {
      time: timeResult,
      space: spaceResult,
      summary: {
        algorithmType: this.detectAlgorithmType(timeResult, spaceResult),
        optimizationSuggestions: this.generateOptimizationSuggestions(timeResult, spaceResult),
        tradeoffs: this.analyzeTradeoffs(timeResult, spaceResult)
      }
    };
  }

  private analyzeTimeComplexity(code: string, language: SupportedLanguage): ComplexityResult {
    switch (language) {
      case 'python':
        return this.pythonParser.parse(code);
      case 'cpp':
        return this.cppParser.parse(code);
      case 'javascript':
        return this.jsParser.parse(code);
      default:
        return this.getUnsupportedResult();
    }
  }

  private getEmptyResult(): ComplexityResult {
    return {
      complexity: 'O(1)',
      explanation: 'No code provided for analysis.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: []
      }
    };
  }

  private getUnsupportedResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Unsupported programming language.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Unsupported language']
      }
    };
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'An error occurred during code analysis. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }

  // Additional utility methods for enhanced analysis
  getComplexityInsights(result: ComplexityResult): string[] {
    const insights: string[] = [];
    
    if (result.details.recursiveCalls > 0) {
      insights.push('🔄 Recursive algorithm detected - consider iterative alternatives for better space complexity');
    }
    
    if (result.details.nestingLevel >= 3) {
      insights.push('⚠️ High nesting level - consider breaking down into smaller functions');
    }
    
    if (result.complexity === 'O(n^2)' && result.details.loops > 0) {
      insights.push('💡 Quadratic complexity - look for opportunities to use hash tables or sorting');
    }
    
    if (result.complexity === 'O(2^n)') {
      insights.push('🚨 Exponential complexity - consider dynamic programming or memoization');
    }
    
    if (result.confidence < 80) {
      insights.push('🤔 Low confidence analysis - complex patterns detected, manual review recommended');
    }
    
    return insights;
  }

  getOptimizationSuggestions(result: ComplexityResult): string[] {
    const suggestions: string[] = [];
    
    switch (result.complexity) {
      case 'O(n^2)':
        suggestions.push('Consider using hash tables for O(1) lookups');
        suggestions.push('Look into sorting + two pointers technique');
        suggestions.push('Evaluate if nested loops can be combined or eliminated');
        break;
        
      case 'O(2^n)':
        suggestions.push('Implement memoization to cache recursive results');
        suggestions.push('Consider dynamic programming approach');
        suggestions.push('Look for overlapping subproblems');
        break;
        
      case 'O(n^3)':
        suggestions.push('Break down into smaller subproblems');
        suggestions.push('Consider matrix multiplication optimizations');
        suggestions.push('Evaluate if some loops can be eliminated');
        break;
    }
    
    return suggestions;
  }

  private detectAlgorithmType(timeResult: ComplexityResult, spaceResult: any): string {
    const patterns = timeResult.details.patterns.join(' ').toLowerCase();

    // Sorting algorithms
    if (patterns.includes('nested') && timeResult.complexity === 'O(n^2)') {
      return 'Quadratic Sorting (Bubble/Selection/Insertion Sort)';
    }

    if (timeResult.complexity === 'O(n log n)' && timeResult.details.recursiveCalls > 0) {
      return 'Efficient Sorting (Merge/Quick Sort)';
    }

    // Search algorithms
    if (timeResult.complexity === 'O(log n)') {
      return 'Binary Search';
    }

    if (timeResult.complexity === 'O(n)' && timeResult.details.loops === 1) {
      return 'Linear Search';
    }

    // Recursive algorithms
    if (timeResult.complexity === 'O(2^n)') {
      return 'Exponential Recursion (e.g., Naive Fibonacci)';
    }

    if (timeResult.details.recursiveCalls > 0 && timeResult.complexity === 'O(n)') {
      return 'Linear Recursion (e.g., Tree Traversal)';
    }

    return 'General Algorithm';
  }

  private generateOptimizationSuggestions(timeResult: ComplexityResult, spaceResult: any): string[] {
    const suggestions: string[] = [];

    // Time complexity optimizations
    switch (timeResult.complexity) {
      case 'O(n^2)':
        suggestions.push('Use hash tables for O(1) lookups instead of nested loops');
        suggestions.push('Consider sorting + two pointers technique');
        suggestions.push('Look into divide-and-conquer approaches');
        break;

      case 'O(2^n)':
        suggestions.push('Implement memoization to cache recursive results');
        suggestions.push('Use dynamic programming for overlapping subproblems');
        suggestions.push('Consider iterative solutions with explicit stack');
        break;

      case 'O(n^3)':
        suggestions.push('Break down into smaller subproblems');
        suggestions.push('Use more efficient algorithms (e.g., Strassen for matrix multiplication)');
        suggestions.push('Consider approximation algorithms if exact solution not required');
        break;
    }

    // Space complexity optimizations
    if (spaceResult.spaceComplexity === 'O(n)' && timeResult.details.recursiveCalls > 0) {
      suggestions.push('Convert recursion to iteration to reduce space complexity');
      suggestions.push('Use tail recursion optimization if available');
    }

    if (spaceResult.details.dataStructures.length > 2) {
      suggestions.push('Minimize auxiliary data structures');
      suggestions.push('Reuse existing data structures where possible');
    }

    return suggestions;
  }

  private analyzeTradeoffs(timeResult: ComplexityResult, spaceResult: any): string[] {
    const tradeoffs: string[] = [];

    // Time vs Space tradeoffs
    if (timeResult.complexity === 'O(n)' && spaceResult.spaceComplexity === 'O(n)') {
      tradeoffs.push('Time-Space Tradeoff: Using extra memory for linear time performance');
    }

    if (timeResult.complexity === 'O(log n)' && spaceResult.spaceComplexity === 'O(n)') {
      tradeoffs.push('Space for Speed: Using additional space to achieve logarithmic time');
    }

    if (timeResult.details.recursiveCalls > 0) {
      tradeoffs.push('Recursion Tradeoff: Elegant code but higher space complexity due to call stack');
    }

    if (spaceResult.details.dataStructures.length > 0) {
      tradeoffs.push('Data Structure Overhead: Additional memory for improved access patterns');
    }

    return tradeoffs;
  }
}

export const enhancedComplexityAnalyzer = new EnhancedComplexityAnalyzer();