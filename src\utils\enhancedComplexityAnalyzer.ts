import { ComplexityResult, SupportedLanguage } from '../types/analysis';
import { PythonParser } from './parsers/pythonParser';
import { CppParser } from './parsers/cppParser';
import { JavaScriptParser } from './parsers/javascriptParser';

class EnhancedComplexityAnalyzer {
  private pythonParser = new PythonParser();
  private cppParser = new CppParser();
  private jsParser = new JavaScriptParser();

  analyzeCode(code: string, language: SupportedLanguage): ComplexityResult {
    if (!code.trim()) {
      return this.getEmptyResult();
    }

    try {
      switch (language) {
        case 'python':
          return this.pythonParser.parse(code);
        case 'cpp':
          return this.cppParser.parse(code);
        case 'javascript':
          return this.jsParser.parse(code);
        default:
          return this.getUnsupportedResult();
      }
    } catch (error) {
      console.error('Analysis failed:', error);
      return this.getErrorResult();
    }
  }

  private getEmptyResult(): ComplexityResult {
    return {
      complexity: 'O(1)',
      explanation: 'No code provided for analysis.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: []
      }
    };
  }

  private getUnsupportedResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Unsupported programming language.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Unsupported language']
      }
    };
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'An error occurred during code analysis. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }

  // Additional utility methods for enhanced analysis
  getComplexityInsights(result: ComplexityResult): string[] {
    const insights: string[] = [];
    
    if (result.details.recursiveCalls > 0) {
      insights.push('🔄 Recursive algorithm detected - consider iterative alternatives for better space complexity');
    }
    
    if (result.details.nestingLevel >= 3) {
      insights.push('⚠️ High nesting level - consider breaking down into smaller functions');
    }
    
    if (result.complexity === 'O(n^2)' && result.details.loops > 0) {
      insights.push('💡 Quadratic complexity - look for opportunities to use hash tables or sorting');
    }
    
    if (result.complexity === 'O(2^n)') {
      insights.push('🚨 Exponential complexity - consider dynamic programming or memoization');
    }
    
    if (result.confidence < 80) {
      insights.push('🤔 Low confidence analysis - complex patterns detected, manual review recommended');
    }
    
    return insights;
  }

  getOptimizationSuggestions(result: ComplexityResult): string[] {
    const suggestions: string[] = [];
    
    switch (result.complexity) {
      case 'O(n^2)':
        suggestions.push('Consider using hash tables for O(1) lookups');
        suggestions.push('Look into sorting + two pointers technique');
        suggestions.push('Evaluate if nested loops can be combined or eliminated');
        break;
        
      case 'O(2^n)':
        suggestions.push('Implement memoization to cache recursive results');
        suggestions.push('Consider dynamic programming approach');
        suggestions.push('Look for overlapping subproblems');
        break;
        
      case 'O(n^3)':
        suggestions.push('Break down into smaller subproblems');
        suggestions.push('Consider matrix multiplication optimizations');
        suggestions.push('Evaluate if some loops can be eliminated');
        break;
    }
    
    return suggestions;
  }
}

export const enhancedComplexityAnalyzer = new EnhancedComplexityAnalyzer();